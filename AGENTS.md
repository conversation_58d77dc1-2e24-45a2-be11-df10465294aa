# Repository Guidelines

## Project Structure & Module Organization
- Source: `src/main/java` (packages under `flink.*`, e.g., `flink.app`, `flink.util`).
- Entrypoints: `flink.app.EventsToS3`, `flink.app.EventsToOSS`, `flink.app.OffsetToNew`.
- Resources: `src/main/resources` (IP DB, runtime properties). Sample `.properties` also exist in repo root.
- Build config: `pom.xml` (Java 8, Flink 1.15, shaded JAR).

## Build, Test, and Development Commands
- Build JAR: `mvn clean package -DskipTests` — outputs `target/lb-bi-sensor-events-to-s3-1.0-SNAPSHOT.jar`.
- Install locally: `mvn clean install`.
- Run locally (Flink CLI):
  - `flink run -c flink.app.EventsToS3 target/lb-bi-sensor-events-to-s3-1.0-SNAPSHOT.jar`
  - Or run a main class from your IDE.
- Branches: staging `oss-staging`, production `prod-aliyun`.

## Coding Style & Naming Conventions
- Java 8; 4-space indentation; keep lines ≤ ~120 cols.
- Packages: lowercase (e.g., `flink.function`). Classes: PascalCase. Methods/fields: camelCase. Constants: UPPER_SNAKE_CASE (see `flink.constant.Constants`).
- Logging: SLF4J — `private static final Logger log = LoggerFactory.getLogger(X.class);`
- Avoid committing secrets; pull from env/Secrets Manager or properties.

## Testing Guidelines
- No tests configured yet. Prefer JUnit 5 when adding.
- Location: `src/test/java` mirroring `src/main/java` packages.
- Naming: `ClassNameTest.java`. Start with pure utils and custom assigners/sinks.
- Run tests: `mvn test`.

## Commit & Pull Request Guidelines
- Commits: concise, imperative subject; English or Chinese acceptable.
  - Examples: `app: add S3 sink and fix offsets` / `修复事件时间异常分流`.
- PRs include: summary and motivation; affected modules (e.g., `flink.app`, `flink.util`); build result (`mvn clean package`); any config keys changed; linked issues and logs/screenshots when behavior changes.

## Security & Configuration Tips
- Do not commit credentials or real endpoints. Use IAM/env for AWS; use Secrets Manager for sensitive values.
- Prefer config in `src/main/resources` or pass via program args; document new keys in README.
- Validate on small Kafka topics before production; monitor S3/OSS paths and alerting.

