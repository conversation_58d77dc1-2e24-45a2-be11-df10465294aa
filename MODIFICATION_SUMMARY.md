# 代码修改总结

## 完成的工作

### 1. 分支切换
- 已切换到测试分支 `oss-staging`
- 确保代码是最新版本

### 2. 为 EventsToS3.java 添加时间过滤逻辑
修改了 `src/main/java/flink/app/EventsToS3.java`，添加了与 `EventsToOSS.java` 相同的异常数据过滤逻辑：

#### 主要修改内容：
- **异常数据过滤条件**：
  ```java
  if(event.contains("%") || event.contains("#") || event.contains("/") || event.contains(":") || time - System.currentTimeMillis() >= 24 * 60 * 60 * 1000 * 3)
  ```
- **异常数据处理**：将异常数据输出到 `sensorErrorDataTag`，并发送飞书告警
- **异常数据存储**：添加了 `errorDataSink()` 方法，将异常数据存储到专门的 OSS 路径
- **3天时间阈值**：过滤掉时间戳超过当前时间3天的数据

### 3. 创建两个新的独立类

#### EventToKafka.java
- **功能**：专门处理发送数据到 Kafka 的逻辑
- **特点**：
  - 包含相同的异常时间数据过滤逻辑
  - 验证 account_channel 一致性
  - 只处理 Kafka 输出，不处理 OSS 存储
  - 使用 `KafkaUtils.KafkaSinkExactlyOnce(config)` 发送数据

#### EventToTrackingLogOSS.java
- **功能**：专门处理发送数据到 OSS 的逻辑
- **特点**：
  - 包含相同的异常时间数据过滤逻辑
  - 验证 account_channel 一致性
  - 处理正常数据和异常数据的 OSS 存储
  - 正常数据存储路径：`config.getString(Constants.S3_EVENT_PATH)`
  - 异常数据存储路径：`oss://canary-lb-bi-event-tracking-log/lb_bi_event_tracking_sensor_event_error_data_d`

## 核心过滤逻辑

所有三个类现在都包含相同的异常数据过滤逻辑：

```java
// 增加数据筛选条件，数据发生时间在未来会被过滤放入异常数据地址，并且设置3天的阈值
if(event.contains("%") || event.contains("#") || event.contains("/") || event.contains(":") || time - System.currentTimeMillis() >= 24 * 60 * 60 * 1000 * 3) {
    context.output(sensorErrorDataTag, eventModel);
    Alert.sensorAlert(config.getString(Constants.feishu_URL), "存在异常数据，已过滤", "数据的Kafka_partition: " + eventModel.getKafka_partition() + "\n 数据在kafka中的offset: " + eventModel.getKafka_offset_index() + "\n 如有需要请查询lb_bi_event_tracking_sensor_event_error_data_d表");
} else if(event != null && eventModel.getType().contains("track")){
    // 处理正常数据
    collector.collect(eventModel);
}
```

## 过滤条件说明

1. **特殊字符过滤**：过滤包含 `%`, `#`, `/`, `:` 的事件名称
2. **时间异常过滤**：过滤时间戳超过当前时间3天（72小时）的数据
3. **数据类型过滤**：只处理 `track` 类型的事件
4. **非空检查**：确保事件名称不为空

## 异常数据处理

- 异常数据会被发送到专门的错误数据存储路径
- 发送飞书告警通知，包含详细的错误信息
- 记录 Kafka 分区和偏移量信息，便于后续排查

## 文件结构

```
src/main/java/flink/app/
├── EventsToS3.java          # 修改后的原文件，包含时间过滤逻辑
├── EventToKafka.java        # 新建：专门处理 Kafka 输出
└── EventToTrackingLogOSS.java # 新建：专门处理 OSS 存储
```

## 下一步建议

1. **测试验证**：建议编写单元测试验证过滤逻辑的正确性
2. **配置检查**：确认相关配置项（如 OSS 路径、Kafka 配置）在测试环境中正确设置
3. **监控告警**：验证飞书告警功能是否正常工作
4. **性能测试**：在测试环境中验证修改后的性能表现
