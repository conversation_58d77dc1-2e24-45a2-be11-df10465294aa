## **安装与运行**

### **环境要求**
- JDK 1.8
- Maven 3.9+

### **安装步骤**
1. 克隆项目到本地：https://longbridge.feishu.cn/wiki/wikcn8knLpw45UczWyYEDv5afyh
   ```bash
   git clone https://${PRIVATE-TOKEN}@gitea.devops.longbridge.dev/data/lb_bi_flink_sensor_events_to_s3.git
   cd your-repo
   ```

2. 构建项目：
    - 使用 Maven：
      ```bash
      mvn clean install
      ```
3. 代码分支：
- 测试环境 oss-staging
- 生产环境 prod-aliyun

4. StreamX部署：
- 测试环境 https://streamx-aliyun.longbridge.xyz/#/flink/app
- 生产环境 https://streamx.longbridge-inc.com/#/flink/app

5. 生产环境Application
- JAREventsToOSSDoublePartition
- JARSensorAnalyze