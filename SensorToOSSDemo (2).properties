## ç¯å¢
my.env=staging
## job_name
job.name=EventsToS3
## é£ä¹¦æ¥è­¦ç¾¤
feishu.url=https://open.feishu.cn/open-apis/bot/v2/hook/83b47f5d-64fc-4d60-bd85-45b526ae586d
## kafka.event.sink.bootstrap.servers
kafka.event.sink.bootstrap.servers=alikafka-post-public-intl-sg-dza3gdb1m0a-1-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-dza3gdb1m0a-2-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-dza3gdb1m0a-3-vpc.alikafka.aliyuncs.com:9092
## kafka.event.group
kafka.event.group=EventsToS3staginggroup
## kafka.event.sink.topic
kafka.event.sink.topic=pub-ods-event-topic-r3p3
## url
s3.event.path=oss://canary-lb-bi-event-tracking-log/lb_bi_sensor_event_d
## test
test=oss://canary-lb-bi-event-tracking-log/lb_bi_sensor_event_d
## kafka.event.source.bootstrap.servers
kafka.event.source.bootstrap.servers=alikafka-post-public-intl-sg-dza3gdb1m0a-1-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-dza3gdb1m0a-2-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-dza3gdb1m0a-3-vpc.alikafka.aliyuncs.com:9092
## kafka.event.source.topic
kafka.event.source.topic=pub-ods-undecoded-event-r3p3
## kafka.event.repair.topic
kafka.event.repair.topic=lb-dwd-sensor-event-fix-r3p3
## checkpoint.dir
checkpoint.dir=oss://canary-lb-bi-ci/flink/SensorAnalyzeToKafka/checkpoint/
