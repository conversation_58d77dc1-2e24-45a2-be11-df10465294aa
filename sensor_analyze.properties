## ç¯å¢
my.env=prod
## job_name
job.name=EventsToS3
## é£ä¹¦æ¥è­¦ç¾¤
feishu.url=https://open.feishu.cn/open-apis/bot/v2/hook/5770ac1d-1599-4ca4-9618-dfca38c71680
## kafka.event.sink.bootstrap.servers
kafka.event.sink.bootstrap.servers=alikafka-post-public-intl-sg-uz63j8tfo01-1-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-uz63j8tfo01-2-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-uz63j8tfo01-3-vpc.alikafka.aliyuncs.com:9092
## kafka.event.group
kafka.event.group=sensor-analyze-test
## kafka.event.sink.topic
kafka.event.sink.topic=pub-ods-event-topic-r3p3
## url
s3.event.path=oss://lb-bi-event-tracking-log/lb_bi_sensor_event_d
## test
test=oss://lb-bi-event-tracking-log/lb_bi_sensor_event_d
## kafka.event.source.bootstrap.servers
kafka.event.source.bootstrap.servers=alikafka-post-public-intl-sg-uz63j8tfo01-1-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-uz63j8tfo01-2-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-uz63j8tfo01-3-vpc.alikafka.aliyuncs.com:9092
## kafka.event.source.topic
kafka.event.source.topic=pub-ods-undecoded-event-r3p3
## kafka.event.repair.topic
kafka.event.repair.topic=lb-dwd-sensor-event-fix-r3p3
## checkpoint.dir
checkpoint.dir=oss://lb-bi-ci/flink/SensorAnalyzeToKafka/checkpoint/
