package deser;

import com.alibaba.fastjson.JSONObject;
import flink.model.EventSourceModel;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.util.serialization.KeyedDeserializationSchema;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * <AUTHOR>
 * @create 2021/5/19 16:48
 */

public class EventSourceModelDesrializationSchema implements KeyedDeserializationSchema<EventSourceModel> {
    private static Logger log = LoggerFactory.getLogger(EventSourceModelDesrializationSchema.class);

    @Override
    public EventSourceModel deserialize(byte[] messageKey, byte[] message, String topic, int partition, long offset) throws IOException {
        EventSourceModel eventSourceModel = null;

        try {
            eventSourceModel = JSONObject.parseObject(new String(message, "utf-8"), EventSourceModel.class);
        } catch (Exception e) {
            log.error("parse json failed {}", new String(message, "utf-8"), e);
        }
        return eventSourceModel;
    }

    @Override
    public boolean isEndOfStream(EventSourceModel nextElement) {
        return false;
    }

    @Override
    public TypeInformation<EventSourceModel> getProducedType() {
        return TypeInformation.of(new TypeHint<EventSourceModel>() {});
    }
}
