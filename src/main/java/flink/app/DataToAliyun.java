package flink.app;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import flink.common.ConfigurationManager;
import flink.constant.Constants;
import flink.function.EventFlatMap;
import flink.model.EventModel;
import flink.util.BaseUtil;
import flink.util.KafkaUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.serialization.SerializationSchema;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.TimeCharacteristic;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumerBase;
import org.apache.flink.streaming.connectors.kafka.partitioner.FlinkKafkaPartitioner;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Properties;

import static org.apache.kafka.clients.producer.ProducerConfig.*;
import static org.apache.kafka.clients.producer.ProducerConfig.TRANSACTION_TIMEOUT_CONFIG;

public class DataToAliyun {

    private static Logger logger = LoggerFactory.getLogger(EventsToS3.class);
    public static void main(String[] args) throws Exception {
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setStreamTimeCharacteristic(TimeCharacteristic.ProcessingTime);
        String myEnv = "prod";
        CheckpointConfig checkpointConfig = env.getCheckpointConfig();
        checkpointConfig.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        checkpointConfig.setTolerableCheckpointFailureNumber(12);
        checkpointConfig.setCheckpointTimeout(30000L);
        checkpointConfig.enableExternalizedCheckpoints(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
//        StateBackend backend2 = new RocksDBStateBackend(
//                prop.getString(Constants.CHECKPOINT_DIR),
//                true);
//
//        env.setStateBackend(backend2);
        RestartStrategies.FailureRateRestartStrategyConfiguration failureRateRestartStrategyConfiguration =
                RestartStrategies.failureRateRestart(
                        10,
                        org.apache.flink.api.common.time.Time.minutes(10),
                        org.apache.flink.api.common.time.Time.seconds(15));
        env.setRestartStrategy(failureRateRestartStrategyConfiguration);
        if ("prod".equalsIgnoreCase(myEnv) || "staging".equalsIgnoreCase(myEnv)) {
            env.enableCheckpointing(300000L);
        }

//        KafkaSource<String> eventModelFlinkKafkaConsumerPub = getAwsSource("pub-ods-dblog-r3p8", "1");
//        DataStreamSource<String> sensorSourceResultPub = env.fromSource(eventModelFlinkKafkaConsumerPub, WatermarkStrategy.noWatermarks(), "pub-ods-dblog-r3p8");
//        sensorSourceResultPub.sinkTo(KafkaSink("pub-ods-dblog-r3p3"));
//
//        KafkaSource<String> eventModelFlinkKafkaConsumerLB = getAwsSource("lb-ods-dblog-r3p8", "2");
//        DataStreamSource<String> sensorSourceResultLB = env.fromSource(eventModelFlinkKafkaConsumerLB, WatermarkStrategy.noWatermarks(), "lb-ods-dblog-r3p8");
//        sensorSourceResultLB.sinkTo(KafkaSink("lb-ods-dblog-r3p3"));
//
        KafkaSource<String> eventModelFlinkKafkaConsumerSensor = getAwsSource("lb-ods-event-r3p1", "3");
        DataStreamSource<String> sensorSourceResultSensor = env.fromSource(eventModelFlinkKafkaConsumerSensor, WatermarkStrategy.noWatermarks(), "lb-ods-event-r3p1");
        sensorSourceResultSensor.sinkTo(KafkaSink("pub-ods-undecoded-event-r3p3"));
//
//        KafkaSource<String> eventModelFlinkKafkaConsumerLBSG = getAwsSource("lb_sg-ods-dblog-r3p8", "4");
//        DataStreamSource<String> sensorSourceResultLBSG = env.fromSource(eventModelFlinkKafkaConsumerLBSG, WatermarkStrategy.noWatermarks(), "lb_sg-ods-dblog-r3p8");
//        sensorSourceResultLBSG.sinkTo(KafkaSink("lb_sg-ods-dblog-r3p3"));
//
//        KafkaSource<String> eventModelFlinkKafkaConsumerLONGPORTTEST = getAwsSource("longport_test-ods-dblog-r3p8", "5");
//        DataStreamSource<String> sensorSourceResultLONGPORTTEST = env.fromSource(eventModelFlinkKafkaConsumerLONGPORTTEST, WatermarkStrategy.noWatermarks(), "longport_test-ods-dblog-r3p8");
//        sensorSourceResultLONGPORTTEST.sinkTo(KafkaSink("longport_test-ods-dblog-r3p3"));
//
//        KafkaSource<String> eventModelFlinkKafkaConsumerHKPANDA = getAwsSource("hk_panda-ods-dblog-r3p8", "6");
//        DataStreamSource<String> sensorSourceResultHKPANDA = env.fromSource(eventModelFlinkKafkaConsumerHKPANDA, WatermarkStrategy.noWatermarks(), "hk_panda-ods-dblog-r3p8");
//        sensorSourceResultHKPANDA.sinkTo(KafkaSink("hk_panda-ods-dblog-r3p3"));

//        KafkaSource<JSONObject> eventModelFlinkKafkaConsumerCDCSTATUS = getAwsSourceCDC("cdc_status_storage", "7");
//        DataStreamSource<JSONObject> sensorSourceResultCDCSTATUS = env.fromSource(eventModelFlinkKafkaConsumerCDCSTATUS, WatermarkStrategy.noWatermarks(), "cdc_status_storage");
//        sensorSourceResultCDCSTATUS.sinkTo(KafkaSinkAws("cdc_status_storage_aws"));
//
//        KafkaSource<JSONObject> eventModelFlinkKafkaConsumerCDCOFFSET = getAwsSourceCDC("cdc_offset_storage", "8");
//        DataStreamSource<JSONObject> sensorSourceResultCDCOFFSET = env.fromSource(eventModelFlinkKafkaConsumerCDCOFFSET, WatermarkStrategy.noWatermarks(), "cdc_offset_storage");
//        sensorSourceResultCDCOFFSET.sinkTo(KafkaSinkAws("cdc_offset_storage_aws"));
//
//        KafkaSource<JSONObject> eventModelFlinkKafkaConsumerCDCCONFIG = getAwsSourceCDC("cdc_configs_storage", "9");
//        DataStreamSource<JSONObject> sensorSourceResultCDCCONFIG = env.fromSource(eventModelFlinkKafkaConsumerCDCCONFIG, WatermarkStrategy.noWatermarks(), "cdc_configs_storage");
//        sensorSourceResultCDCCONFIG.sinkTo(KafkaSinkAws("cdc_configs_storage_aws"));

        //portfolio-atm_events
        //portfolio-atm-lb-sg_events
        //portfolio-atm-hk-panda_events
        //portfolio-atm-longport-test_events
//        KafkaSource<JSONObject> eventModelFlinkKafkaConsumerATMEVENTS = getAwsSourceCDC("portfolio-atm_events", "10");
//        DataStreamSource<JSONObject> sensorSourceResultATMEVENTS = env.fromSource(eventModelFlinkKafkaConsumerATMEVENTS, WatermarkStrategy.noWatermarks(), "portfolio-atm_events");
//        sensorSourceResultATMEVENTS.sinkTo(KafkaSinkAwsCore("portfolio-atm_events"));
//
//        KafkaSource<JSONObject> eventModelFlinkKafkaConsumerATMEVENTSLBSG = getAwsSourceCDC("portfolio-atm-lb-sg_events", "11");
//        DataStreamSource<JSONObject> sensorSourceResultATMEVENTSLBSG = env.fromSource(eventModelFlinkKafkaConsumerATMEVENTSLBSG, WatermarkStrategy.noWatermarks(), "portfolio-atm-lb-sg_events");
//        sensorSourceResultATMEVENTSLBSG.sinkTo(KafkaSinkAwsCore("portfolio-atm-lb-sg_events"));
//
//        KafkaSource<JSONObject> eventModelFlinkKafkaConsumerATMEVENTSHKPANDA = getAwsSourceCDC("portfolio-atm-hk-panda_events", "12");
//        DataStreamSource<JSONObject> sensorSourceResultATMEVENTSHKPANDA = env.fromSource(eventModelFlinkKafkaConsumerATMEVENTSHKPANDA, WatermarkStrategy.noWatermarks(), "portfolio-atm-hk-panda_events");
//        sensorSourceResultATMEVENTSHKPANDA.sinkTo(KafkaSinkAwsCore("portfolio-atm-hk-panda_events"));
//
//        KafkaSource<JSONObject> eventModelFlinkKafkaConsumerATMEVENTSLONGPORTTEST = getAwsSourceCDC("portfolio-atm-longport-test_events", "13");
//        DataStreamSource<JSONObject> sensorSourceResultATMEVENTSLONGPORTTEST = env.fromSource(eventModelFlinkKafkaConsumerATMEVENTSLONGPORTTEST, WatermarkStrategy.noWatermarks(), "portfolio-atm-longport-test_events");
//        sensorSourceResultATMEVENTSLONGPORTTEST.sinkTo(KafkaSinkAwsCore("portfolio-atm-longport-test_events"));

        env.execute(DataToAliyun.class.getSimpleName());
    }

    public static Properties initProp() {

        Properties propKafka = new Properties();

        propKafka.setProperty(FlinkKafkaConsumerBase.KEY_PARTITION_DISCOVERY_INTERVAL_MILLIS, "10000");
//        InputStream resourceAsStream = sdp2mysql.class.getClassLoader().getResourceAsStream("prod.properties");
//        try {
//            propBasic.load(resourceAsStream);
//        } catch ( IOException e) {
//            System.out.println(e.getMessage());
//            e.printStackTrace();
//        }
        propKafka.setProperty(ACKS_CONFIG, "-1");
        propKafka.setProperty(KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        propKafka.setProperty(VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.ByteArrayDeserializer");

        //RecordAccumulator缓冲区大小32M
        propKafka.setProperty(BUFFER_MEMORY_CONFIG, "33554432");

        //131KB
        propKafka.setProperty(BATCH_SIZE_CONFIG, "131072");

        //100ms
        propKafka.setProperty(LINGER_MS_CONFIG, "100");

        propKafka.setProperty(MAX_REQUEST_SIZE_CONFIG, "83886080");
        propKafka.setProperty(RETRIES_CONFIG, "10");
        propKafka.setProperty(RETRY_BACKOFF_MS_CONFIG, "500");
        propKafka.setProperty(TRANSACTION_TIMEOUT_CONFIG, 60 * 5 * 1000 + "");

        // b-3.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092,b-2.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092,b-1.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092
//        propKafka.setProperty(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, config.getString(Constants.KAFKA_EVENT_SOURCE_BOOTSTRAP_SERVERS));
//        propKafka.setProperty(ConsumerConfig.GROUP_ID_CONFIG, config.getString(Constants.JOB_NAME) + config.getString(Constants.MY_ENV) + "group");
        propKafka.setProperty(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
//        propKafka.setProperty(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        return propKafka;
    }

    public static KafkaSource<String> getAwsSource(String topic, String flag) {
//        StringBuilder sb = new StringBuilder();
//        String[] split = groupId.replaceAll("-", "_").split("_");
//        for (String s : split) {
//            sb.append(s, 0, 1);
//        }
//        String simpleGroupId = sb.toString();
        Properties prop = initProp();
//        prop.setProperty("max.partition.fetch.bytes", "52428800");
//        prop.setProperty("fetch.message.max.bytes", "52428800");
        return KafkaSource.<String>builder()
                .setBootstrapServers("b-3.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092,b-2.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092,b-1.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092,b-4.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092,b-5.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092,b-6.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092")
                .setTopics(topic)
                .setGroupId("aws-test" + flag)
                .setClientIdPrefix("aws-test" + flag)
                .setProperties(prop)
                .setStartingOffsets(OffsetsInitializer.latest())
                .setDeserializer(new KafkaRecordDeserializationSchema<String>() {
                    @Override
                    public void deserialize(ConsumerRecord<byte[], byte[]> record, Collector<String> out) throws IOException {
                        //todo 输出日志待优化
                        try {
                            if (record == null || record.value() == null || record.value().length == 0) {
                                // logger.error("deserialize error");
                                return;
                            } else {
                                out.collect(new String(record.value(), StandardCharsets.UTF_8));
                            }
                        } catch (Exception e) {
//                            logger.error("deserialize failed.\n" + record);
//                            logger.error(e.getMessage(), e);
                            throw new RuntimeException(e);
                        }
                    }

                    @Override
                    public TypeInformation<String> getProducedType() {
                        return TypeInformation.of(String.class);
                    }
                })
                .build();
    }

    public static KafkaSource<JSONObject> getAwsSourceCDC(String topic, String flag) {
//        StringBuilder sb = new StringBuilder();
//        String[] split = groupId.replaceAll("-", "_").split("_");
//        for (String s : split) {
//            sb.append(s, 0, 1);
//        }
//        String simpleGroupId = sb.toString();
        Properties prop = initProp();
//        prop.setProperty("max.partition.fetch.bytes", "52428800");
//        prop.setProperty("fetch.message.max.bytes", "52428800");
        return KafkaSource.<JSONObject>builder()
                .setBootstrapServers("b-3.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092,b-2.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092,b-1.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092,b-4.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092,b-5.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092,b-6.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092")
                .setTopics(topic)
                .setGroupId("aws-test" + flag)
                .setClientIdPrefix("aws-test" + flag)
                .setProperties(prop)
                .setStartingOffsets(OffsetsInitializer.earliest())
                .setDeserializer(new KafkaRecordDeserializationSchema<JSONObject>() {
                    @Override
                    public TypeInformation<JSONObject> getProducedType() {
                        return TypeInformation.of(JSONObject.class);
                    }

                    @Override
                    public void deserialize(ConsumerRecord<byte[], byte[]> record, Collector<JSONObject> out) throws IOException {
                        //todo 输出日志待优化
                        try {
                            if (record == null || record.value() == null || record.value().length == 0) {
                                // logger.error("deserialize error");
                                return;
                            } else {
                                String key = new String(record.key(), StandardCharsets.UTF_8);
                                String value = new String(record.value(), StandardCharsets.UTF_8);

                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put("key", key);
                                jsonObject.put("value", value);

                                out.collect(jsonObject);
                            }
                        } catch (Exception e) {
                            logger.error("deserialize failed.\n" + record);
                            logger.error(e.getMessage(), e);

                            throw new RuntimeException(e);
                        }
                    }
                })
                .build();
    }

    public static KafkaSink KafkaSink(String topic) {
        Properties prop = new Properties();
//        UUID transactionID = UUID.randomUUID();
//        prop.setProperty("transaction.timeout.ms", "900000");
        KafkaSink<String> sink = KafkaSink.<String>builder()
                .setKafkaProducerConfig(prop)
                .setBootstrapServers("alikafka-post-public-intl-sg-uz63j8tfo01-1-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-uz63j8tfo01-2-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-uz63j8tfo01-3-vpc.alikafka.aliyuncs.com:9092")
                .setRecordSerializer(KafkaRecordSerializationSchema.builder()
                                .setTopic(topic)
                                .setPartitioner(new FlinkKafkaPartitioner<String>() {
                                    @Override
                                    public int partition(String record, byte[] key, byte[] value, String targetTopic, int[] partitions) {
//                                String distinctId = record.getDistinct_id();
                                        return Math.abs(record.hashCode()) % partitions.length;
                                    }
                                })
                                .setValueSerializationSchema(new SerializationSchema<String>() {
                                    @Override
                                    public byte[] serialize(String s) {
                                        return new byte[0];
                                    }
                                })
                                .build()
                )
                .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                .build();


        return sink;
    }

    public static KafkaSink KafkaSinkAws(String topic) {
        Properties prop = new Properties();
        KafkaSink<JSONObject> sink = KafkaSink.<JSONObject>builder()
                .setKafkaProducerConfig(prop)
                .setBootstrapServers("alikafka-post-public-intl-sg-uz63j8tfo01-1-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-uz63j8tfo01-2-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-uz63j8tfo01-3-vpc.alikafka.aliyuncs.com:9092")
                .setRecordSerializer(KafkaRecordSerializationSchema.builder()
                        .setTopic(topic)
                        .setKeySerializationSchema(new SerializationSchema<JSONObject>() {
                            @Override
                            public byte[] serialize(JSONObject s) {
//                                JSONObject jsonObject = JSONObject.parseObject(s);
                                String value = s.getString("key");
                                return value.getBytes();
                            }
                        })
                        .setValueSerializationSchema(new SerializationSchema<JSONObject>() {
                            @Override
                            public byte[] serialize(JSONObject s) {
//                                JSONObject jsonObject = JSONObject.parseObject(s);
                                String value = s.getString("value");
                                return value.getBytes();
                            }
                        })
                        .build()
                )
//                .setDeliverGuarantee(DeliveryGuarantee.EXACTLY_ONCE)
                .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
//                .setTransactionalIdPrefix(transactionID.toString())
                .build();
        return sink;
    }

    public static KafkaSink KafkaSinkAwsCore(String topic) {
        Properties prop = new Properties();
        KafkaSink<JSONObject> sink = KafkaSink.<JSONObject>builder()
                .setKafkaProducerConfig(prop)
                .setBootstrapServers("b-1.prod-lb-core-portfolio.3jjq7d.c3.kafka.ap-east-1.amazonaws.com:9092,b-3.prod-lb-core-portfolio.3jjq7d.c3.kafka.ap-east-1.amazonaws.com:9092,b-2.prod-lb-core-portfolio.3jjq7d.c3.kafka.ap-east-1.amazonaws.com:9092")
                .setRecordSerializer(KafkaRecordSerializationSchema.builder()
                                .setTopic(topic)
                                .setKeySerializationSchema(new SerializationSchema<JSONObject>() {
                                    @Override
                                    public byte[] serialize(JSONObject s) {
//                                JSONObject jsonObject = JSONObject.parseObject(s);
                                        String value = s.getString("key");
                                        return value.getBytes();
                                    }
                                })
                                .setValueSerializationSchema(new SerializationSchema<JSONObject>() {
                                    @Override
                                    public byte[] serialize(JSONObject s) {
//                                JSONObject jsonObject = JSONObject.parseObject(s);
                                        String value = s.getString("value");
                                        return value.getBytes();
                                    }
                                })
                                .build()
                )
//                .setDeliverGuarantee(DeliveryGuarantee.EXACTLY_ONCE)
                .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
//                .setTransactionalIdPrefix(transactionID.toString())
                .build();
        return sink;
    }
}
