package flink.app;

import com.alibaba.fastjson.JSONObject;
import flink.common.ConfigurationManager;
import flink.constant.Constants;
import flink.function.EventFlatMap;
import flink.model.Alert;
import flink.model.EventModel;
import flink.util.BaseUtil;
import flink.util.IpUtils;
import flink.util.KafkaUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.lionsoul.ip2region.xdb.Searcher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class EventToKafka {
    private static Logger log = LoggerFactory.getLogger(EventToKafka.class);
    
    public static void main(String[] args) throws Exception {
        ConfigurationManager config = new ConfigurationManager(args);
        StreamExecutionEnvironment env = BaseUtil.getEnv(config);
        env.getCheckpointConfig().setCheckpointInterval(30000l);

        String dbPath = IpUtils.createFtlFileByFtlArray();
        Searcher.newWithFileOnly(dbPath);

        KafkaSource<JSONObject> eventModelFlinkKafkaConsumer = KafkaUtils.getSourceWithOffset(config);
        KafkaSource<JSONObject> sourceWithOffset = KafkaUtils.getSourceWithOffsetRepair(config);

        try {
            // 神策修复数据
            // DataStreamSource<JSONObject> sensorSourceRepair = env.fromSource(sourceWithOffset, WatermarkStrategy.noWatermarks(), "SensorSourceRepair");

            // 正常神策数据
            DataStreamSource<JSONObject> sensorSourceResult = env.fromSource(eventModelFlinkKafkaConsumer, WatermarkStrategy.noWatermarks(), "SensorSource");

            // 合并工作流
            // DataStream<JSONObject> sensorSourceResult = sensorSource.union(sensorSourceRepair);

            // 解析神策数据
            SingleOutputStreamOperator<EventModel> eventFlatMapStream = sensorSourceResult.flatMap(new EventFlatMap(config));

            OutputTag<EventModel> sensorErrorDataTag = new OutputTag<EventModel>("SensorErrorDataTag"){};

            SingleOutputStreamOperator<EventModel> kafkaDataResultStream = eventFlatMapStream.process(new ProcessFunction<EventModel, EventModel>() {
                @Override
                public void processElement(EventModel eventModel, ProcessFunction<EventModel, EventModel>.Context context, Collector<EventModel> collector) throws Exception {
                    String event = eventModel.getEvent();
                    Long time = eventModel.getTime();
                    
                    // 增加数据筛选条件，数据发生时间在未来会被过滤放入异常数据地址，并且设置3天的阈值
                    if(event.contains("%") || event.contains("#") || event.contains("/") || event.contains(":") || time - System.currentTimeMillis() >= 24 * 60 * 60 * 1000 * 3) {
                        context.output(sensorErrorDataTag, eventModel);
                        Alert.sensorAlert(config.getString(Constants.feishu_URL), "存在异常数据，已过滤", "数据的Kafka_partition: " + eventModel.getKafka_partition() + "\n 数据在kafka中的offset: " + eventModel.getKafka_offset_index() + "\n 如有需要请查询lb_bi_event_tracking_sensor_event_error_data_d表");
                    } else if(event != null && eventModel.getType().contains("track")){
                        try {
                            // 验证 account_channel 一致性
                            String accountChannel = JSONObject.parseObject(eventModel.getProperties()).getString("account_channel") == null ? "unknown": JSONObject.parseObject(eventModel.getProperties()).getString("account_channel");
                            if("".equalsIgnoreCase(accountChannel)){
                                accountChannel = "unknown";
                            }

                            if((!eventModel.getAccount_channel().equalsIgnoreCase(accountChannel))){
                                Alert.sensorAlert(config.getString(Constants.feishu_URL), "account_channel与properties不匹配", "kafka中data: " + eventModel + "\n properties中account_channel: " + accountChannel);
                            }
                            
                            collector.collect(eventModel);
                        } catch (Exception e) {
                            log.error("eventModel : " + eventModel);
                            throw new RuntimeException(e);
                        }
                    }
                }
            });

            DataStream<EventModel> errorDataStream = kafkaDataResultStream.getSideOutput(sensorErrorDataTag);

            //发送数据到kafka
            KafkaSink kafkaSink = KafkaUtils.KafkaSinkExactlyOnce(config);
            kafkaDataResultStream.map(data -> data.toJson().toString()).sinkTo(kafkaSink);

            env.execute(EventToKafka.class.getSimpleName());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }
}
