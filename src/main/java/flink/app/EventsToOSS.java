package flink.app;
import com.alibaba.fastjson.JSONObject;
import flink.assigner.day.DayEventBucketAssigner;
import flink.assigner.day.DayEventBucketAssignerTest;
import flink.common.ConfigurationManager;
import flink.constant.Constants;
import flink.function.EventFlatMap;
import flink.model.Alert;
import flink.model.EventModel;
import flink.util.BaseUtil;
import flink.util.KafkaUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.core.fs.Path;
import org.apache.flink.formats.parquet.avro.ParquetAvroWriters;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.api.functions.sink.filesystem.StreamingFileSink;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumerBase;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Properties;
import static flink.util.KafkaUtils.initProp;
import static org.apache.kafka.clients.producer.ProducerConfig.*;
import static org.apache.kafka.clients.producer.ProducerConfig.TRANSACTION_TIMEOUT_CONFIG;
public class EventsToOSS {
    private static Logger log = LoggerFactory.getLogger(EventsToOSS.class);
    public static void main(String[] args) throws Exception {
        ConfigurationManager config = new ConfigurationManager(args);
        StreamExecutionEnvironment env = BaseUtil.getEnv(config);
        env.getCheckpointConfig().setCheckpointInterval(30000l);
        KafkaSource<JSONObject> eventModelFlinkKafkaConsumer = getSourceWithOffset(config);
        try {
            // 神策修复数据
            // 正常神策数据
            DataStreamSource<JSONObject> sensorSource = env.fromSource(eventModelFlinkKafkaConsumer, WatermarkStrategy.noWatermarks(), "SensorSource");
            // 合并工作流
            OutputTag<EventModel> sensorErrorDataTag = new OutputTag<EventModel>("SensorErrorDataTag"){};
            // 解析神策数据
            SingleOutputStreamOperator<EventModel> eventFlatMapStream = sensorSource.flatMap(new EventFlatMap(config));
            SingleOutputStreamOperator<EventModel> S3DataResultStream = eventFlatMapStream.process(new ProcessFunction<EventModel, EventModel>() {
                @Override
                public void processElement(EventModel eventModel, ProcessFunction<EventModel, EventModel>.Context context, Collector<EventModel> collector) throws Exception {
                    String event = eventModel.getEvent();
                    Long time = eventModel.getTime();
                    // 增加数据筛选条件，数据发生时间在未来会被过滤放入异常数据地址，并且设置3天的阈值
                    if(event.contains("%") || event.contains("#") || event.contains("/") || event.contains(":") || time - System.currentTimeMillis() >= 24 * 60 * 60 * 1000 * 3) {
                        context.output(sensorErrorDataTag, eventModel);
                        Alert.sensorAlert(config.getString(Constants.feishu_URL), "存在异常数据，已过滤", "数据的Kafka_partition: " + eventModel.getKafka_partition() + "\n 数据在kafka中的offset: " + eventModel.getKafka_offset_index() + "\n 如有需要请查询lb_bi_event_tracking_sensor_event_error_data_d表");
                    } else if(event != null && eventModel.getType().contains("track")){
                        try {
                            collector.collect(eventModel);
                        } catch (Exception e) {
                            log.error("eventModel : " + eventModel);
                            throw new RuntimeException(e);
                        }
                    }
                }
            });
            DataStream<EventModel> errorDataStrem = S3DataResultStream.getSideOutput(sensorErrorDataTag);
            //发送数据到s3
            StreamingFileSink<EventModel> eventModelStreamingFileSink = eventSink(config);
            S3DataResultStream.addSink(eventModelStreamingFileSink);
            //异常发送数据到s3
            StreamingFileSink<EventModel> errorDataStreamFileSink = errorDataSink();
            errorDataStrem.addSink(errorDataStreamFileSink);

            //发送数据到kafka
            KafkaSink kafkaSink = KafkaUtils.KafkaSinkExactlyOnce(config);
            eventFlatMapStream.map(data -> data.toJson().toString()).sinkTo(kafkaSink);

            env.execute(EventsToOSS.class.getSimpleName());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }
    private static StreamingFileSink<EventModel> eventSink(ConfigurationManager config) {
        DayEventBucketAssignerTest<EventModel> myBucketAssigner = new DayEventBucketAssignerTest<>();
        String path = "oss://canary-lb-bi-event-tracking-log/lb_bi_event_tracking_sensor_event_d";
        StreamingFileSink<EventModel> build4 = StreamingFileSink
                .forBulkFormat(new Path(path),
                        ParquetAvroWriters.forReflectRecord(EventModel.class))
                .withBucketAssigner(myBucketAssigner)
                .build();
        return build4;
    }
    private static StreamingFileSink<EventModel> errorDataSink() {
        DayEventBucketAssignerTest<EventModel> myBucketAssigner = new DayEventBucketAssignerTest<>();
        String path = "oss://canary-lb-bi-event-tracking-log/lb_bi_event_tracking_sensor_event_error_data_d";
        StreamingFileSink<EventModel> build4 = StreamingFileSink
                .forBulkFormat(new Path(path),
                        ParquetAvroWriters.forReflectRecord(EventModel.class))
                .withBucketAssigner(myBucketAssigner)
                .build();
        return build4;
    }
    public static KafkaSource<JSONObject> getSourceWithOffsetRepair(ConfigurationManager config) {
        Properties prop = initProp(config);
        return KafkaSource.<JSONObject>builder()
                .setBootstrapServers(config.getString(Constants.KAFKA_EVENT_SOURCE_BOOTSTRAP_SERVERS))
                .setTopics(config.getString(Constants.KAFKA_EVENT_REPAIR_TOPIC))
                .setGroupId("Double-Partition-Sensor")
                .setProperties(prop)
                .setStartingOffsets(OffsetsInitializer.latest())
                .setDeserializer(new KafkaRecordDeserializationSchema<JSONObject>() {
                                     @Override
                                     public TypeInformation<JSONObject> getProducedType() {
                                         return TypeInformation.of(JSONObject.class);
                                     }
                                     @Override
                                     public void deserialize(ConsumerRecord<byte[], byte[]> record, Collector<JSONObject> out) throws IOException {
                                         String jsonString = new String(record.value(), StandardCharsets.UTF_8);
                                         try {
                                             JSONObject jsonObject = JSONObject.parseObject(jsonString);
                                             out.collect(jsonObject);
                                         } catch (Exception e) {
                                             log.error(e.getMessage(), e);
                                         }
                                     }
                                 }
                ).build();
    }
    public static KafkaSource<JSONObject> getSourceWithOffset(ConfigurationManager config) {
//        StringBuilder sb = new StringBuilder();
//        String[] split = groupId.replaceAll("-", "_").split("_");
//        for (String s : split) {
//            sb.append(s, 0, 1);
//        }
//        String simpleGroupId = sb.toString();
        Properties prop = initProp(config);
//        prop.setProperty("max.partition.fetch.bytes", "52428800");
//        prop.setProperty("fetch.message.max.bytes", "52428800");
        return KafkaSource.<JSONObject>builder()
                .setBootstrapServers(config.getString(Constants.KAFKA_EVENT_SOURCE_BOOTSTRAP_SERVERS))
                .setTopics(config.getString(Constants.KAFKA_EVENT_SOURCE_TOPIC))
//                .setGroupId(config.getString(Constants.KAFKA_EVENT_GROUP))
                .setGroupId("Double-Partition-Sensor")
                .setProperties(prop)
                .setStartingOffsets(OffsetsInitializer.latest())
//                .setStartingOffsets(OffsetsInitializer.timestamp(1721761662000l))
                .setDeserializer(new KafkaRecordDeserializationSchema<JSONObject>() {
                                     @Override
                                     public TypeInformation<JSONObject> getProducedType() {
                                         return TypeInformation.of(JSONObject.class);
                                     }
                                     @Override
                                     public void deserialize(ConsumerRecord<byte[], byte[]> record, Collector<JSONObject> out) throws IOException {
                                         String jsonString = new String(record.value(), StandardCharsets.UTF_8);
                                         try {
                                             JSONObject jsonObject = JSONObject.parseObject(jsonString);
                                             jsonObject.put(Constants.KAFKA_OFFSET_KEY, record.offset());
                                             jsonObject.put(Constants.KAFKA_PARTITION_KEY, record.partition());
                                             jsonObject.put(Constants.KAFKA_GROUP_KEY, config.getString(Constants.KAFKA_EVENT_GROUP));
                                             out.collect(jsonObject);
                                         } catch (Exception e) {
                                             log.error(e.getMessage(), e);
                                         }
                                     }
                                 }
                ).build();
    }
    public static Properties initProp(ConfigurationManager config){
        Properties propKafka = new Properties();
        propKafka.setProperty(FlinkKafkaConsumerBase.KEY_PARTITION_DISCOVERY_INTERVAL_MILLIS, "10000");
//        InputStream resourceAsStream = sdp2mysql.class.getClassLoader().getResourceAsStream("prod.properties");
//        try {
//            propBasic.load(resourceAsStream);
//        } catch ( IOException e) {
//            System.out.println(e.getMessage());
//            e.printStackTrace();
//        }
        propKafka.setProperty(ACKS_CONFIG, "-1");
        propKafka.setProperty(KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        propKafka.setProperty(VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.ByteArrayDeserializer");
        //RecordAccumulator缓冲区大小32M
        propKafka.setProperty(BUFFER_MEMORY_CONFIG, "33554432");
        //131KB
        propKafka.setProperty(BATCH_SIZE_CONFIG, "131072");
        //100ms
        propKafka.setProperty(LINGER_MS_CONFIG, "100");
        propKafka.setProperty(MAX_REQUEST_SIZE_CONFIG, "83886080");
        propKafka.setProperty(RETRIES_CONFIG, "10");
        propKafka.setProperty(RETRY_BACKOFF_MS_CONFIG, "500");
        propKafka.setProperty(TRANSACTION_TIMEOUT_CONFIG, 60 * 5 * 1000 + "");
        // b-3.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092,b-2.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092,b-1.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092
        propKafka.setProperty(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, config.getString(Constants.KAFKA_EVENT_SOURCE_BOOTSTRAP_SERVERS));
        propKafka.setProperty(ConsumerConfig.GROUP_ID_CONFIG, "Double-Partition-Sensor");
        propKafka.setProperty(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
//        propKafka.setProperty(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        return propKafka;
    }
}