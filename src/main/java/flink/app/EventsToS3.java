package flink.app;

import com.alibaba.fastjson.JSONObject;
import flink.assigner.day.DayEventBucketAssigner;
import flink.common.ConfigurationManager;
import flink.constant.Constants;
import flink.function.EventFlatMap;
import flink.model.EventModel;
import flink.util.BaseUtil;
import flink.util.KafkaUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.core.fs.Path;
import org.apache.flink.formats.parquet.avro.ParquetAvroWriters;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.api.functions.sink.filesystem.PartFileInfo;
import org.apache.flink.streaming.api.functions.sink.filesystem.RollingPolicy;
import org.apache.flink.streaming.api.functions.sink.filesystem.StreamingFileSink;
import org.apache.flink.streaming.api.functions.sink.filesystem.rollingpolicies.CheckpointRollingPolicy;
import org.apache.flink.streaming.api.functions.sink.filesystem.rollingpolicies.DefaultRollingPolicy;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

public class EventsToS3 {
    private static Logger log = LoggerFactory.getLogger(EventsToS3.class);
    public static void main(String[] args) throws Exception {
        ConfigurationManager config = new ConfigurationManager(args);
        StreamExecutionEnvironment env = BaseUtil.getEnv(config);

        env.getCheckpointConfig().setCheckpointInterval(60000l);

        KafkaSource<JSONObject> eventModelFlinkKafkaConsumer = KafkaUtils.getSourceWithOffset(config);

        KafkaSource<JSONObject> sourceWithOffset = KafkaUtils.getSourceWithOffsetRepair(config);


        try {
            // 神策修复数据
            DataStreamSource<JSONObject> sensorSourceRepair = env.fromSource(sourceWithOffset, WatermarkStrategy.noWatermarks(), "SensorSourceRepair");

            // 正常神策数据
            DataStreamSource<JSONObject> sensorSource = env.fromSource(eventModelFlinkKafkaConsumer, WatermarkStrategy.noWatermarks(), "SensorSource");

            // 合并工作流
            DataStream<JSONObject> sensorSourceResult = sensorSource.union(sensorSourceRepair);

            // 解析神策数据
            SingleOutputStreamOperator<EventModel> eventFlatMapStream = sensorSourceResult.flatMap(new EventFlatMap(config));

            OutputTag<EventModel> kafkaDataTag = new OutputTag<EventModel>("kafkaDataTag"){};

            SingleOutputStreamOperator<EventModel> S3DataResultStream = eventFlatMapStream.process(new ProcessFunction<EventModel, EventModel>() {
                @Override
                public void processElement(EventModel eventModel, ProcessFunction<EventModel, EventModel>.Context context, Collector<EventModel> collector) throws Exception {
                    EventModel eventModelKafka = eventModel;
                    context.output(kafkaDataTag, eventModelKafka);
                    if(eventModel.getEvent() != null && eventModel.getType().contains("track")){
                        try {
                            collector.collect(eventModel);
                        } catch (Exception e) {
                            log.error("eventModel : " + eventModel);
                            throw new RuntimeException(e);
                        }
                    }
                }
            });

            DataStream<EventModel> KafkaDataResultStream = S3DataResultStream.getSideOutput(kafkaDataTag);

            //发送数据到s3
            StreamingFileSink<EventModel> eventModelStreamingFileSink = eventSink(config);
            S3DataResultStream.addSink(eventModelStreamingFileSink);

            //发送数据到kafka
            KafkaSink kafkaSink = KafkaUtils.KafkaSinkExactlyOnce(config);
            KafkaDataResultStream.map(data -> data.toJson().toString()).sinkTo(kafkaSink);

            env.execute(EventsToS3.class.getSimpleName());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    private static StreamingFileSink<EventModel> eventSink(ConfigurationManager config) {
        DayEventBucketAssigner<EventModel> myBucketAssigner = new DayEventBucketAssigner<>();
        String path = config.getString(Constants.S3_EVENT_PATH);
        StreamingFileSink<EventModel> build4 = StreamingFileSink
                .forBulkFormat(new Path(path),
                        ParquetAvroWriters.forReflectRecord(EventModel.class))
                .withBucketAssigner(myBucketAssigner)
//                .withRollingPolicy()
                .withBucketCheckInterval(10000)
                .build();
        return build4;
    }

}
