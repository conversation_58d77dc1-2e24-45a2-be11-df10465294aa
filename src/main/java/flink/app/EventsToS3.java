package flink.app;

import com.alibaba.fastjson.JSONObject;
import flink.assigner.day.DayEventBucketAssigner;
import flink.assigner.day.DayEventBucketAssignerTest;
import flink.common.ConfigurationManager;
import flink.constant.Constants;
import flink.function.EventFlatMap;
import flink.model.Alert;
import flink.model.EventModel;
import flink.util.BaseUtil;
import flink.util.IpUtils;
import flink.util.KafkaUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.core.fs.Path;
import org.apache.flink.formats.parquet.avro.ParquetAvroWriters;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.api.functions.sink.filesystem.StreamingFileSink;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.lionsoul.ip2region.xdb.Searcher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static flink.util.KafkaUtils.getSensorFlinkKafkaProducer;

public class EventsToS3 {
    private static Logger log = LoggerFactory.getLogger(EventsToS3.class);
    public static void main(String[] args) throws Exception {
        ConfigurationManager config = new ConfigurationManager(args);
        StreamExecutionEnvironment env = BaseUtil.getEnv(config);

        String dbPath = IpUtils.createFtlFileByFtlArray();

        Searcher.newWithFileOnly(dbPath);

        KafkaSource<JSONObject> eventModelFlinkKafkaConsumer = KafkaUtils.getSourceWithOffset(config);

        KafkaSource<JSONObject> sourceWithOffset = KafkaUtils.getSourceWithOffsetRepair(config);

        // 神策修复数据
//        DataStreamSource<JSONObject> sensorSourceRepair = env.fromSource(sourceWithOffset, WatermarkStrategy.noWatermarks(), "SensorSourceRepair");

        // 正常神策数据
        DataStreamSource<JSONObject> sensorSourceResult = env.fromSource(eventModelFlinkKafkaConsumer, WatermarkStrategy.noWatermarks(), "SensorSource");

        // 合并工作流
//        DataStream<JSONObject> sensorSourceResult = sensorSource.union(sensorSourceRepair);

        // 解析神策数据
        SingleOutputStreamOperator<EventModel> eventFlatMapStream = sensorSourceResult.flatMap(new EventFlatMap(config));

        OutputTag<EventModel> kafkaDataTag = new OutputTag<EventModel>("kafkaDataTag"){};

        OutputTag<EventModel> OSSDataTag = new OutputTag<EventModel>("OSSDataTag"){};

        SingleOutputStreamOperator<EventModel> S3DataResultStream = eventFlatMapStream.process(new ProcessFunction<EventModel, EventModel>() {
            @Override
            public void processElement(EventModel eventModel, ProcessFunction<EventModel, EventModel>.Context context, Collector<EventModel> collector) throws Exception {
                EventModel eventModelKafka = eventModel;
                context.output(kafkaDataTag, eventModelKafka);
                context.output(OSSDataTag, eventModel);

                if(eventModel.getEvent() != null && eventModel.getType().contains("track")){
                    collector.collect(eventModel);
                }

                String accountChannel = JSONObject.parseObject(eventModelKafka.getProperties()).getString("account_channel") == null ? "unknown": JSONObject.parseObject(eventModelKafka.getProperties()).getString("account_channel");
                if("".equalsIgnoreCase(accountChannel)){
                    accountChannel = "unknown";
                }

                if((!eventModel.getAccount_channel().equalsIgnoreCase(accountChannel))){
                    Alert.sensorAlert(config.getString(Constants.feishu_URL), "account_channel与properties不匹配", "kafka中data: " + eventModelKafka + "\n OSS中data: " + eventModel);
                }

            }
        });

        DataStream<EventModel> KafkaDataResultStream = S3DataResultStream.getSideOutput(kafkaDataTag);
        DataStream<EventModel> OSSDataResultStream = S3DataResultStream.getSideOutput(OSSDataTag);

        //发送数据到s3
        StreamingFileSink<EventModel> eventModelStreamingFileSink = eventSink(config);
        OSSDataResultStream.addSink(eventModelStreamingFileSink);

        //发送数据到kafka
//        FlinkKafkaProducer<EventModel> sensorFlinkKafkaProducer = getSensorFlinkKafkaProducer(config);
//        KafkaDataResultStream.addSink(kafkaSink);
//        KafkaSink kafkaSink = KafkaUtils.KafkaSinkExactlyOnce(config);
//        KafkaDataResultStream.map(data -> data.toJson().toString()).sinkTo(kafkaSink);
        KafkaSink kafkaSink = KafkaUtils.KafkaSinkExactlyOnce(config);
        KafkaDataResultStream.map(data -> data.toJson().toString()).sinkTo(kafkaSink);

        env.execute(EventsToS3.class.getSimpleName());
    }

    private static StreamingFileSink<EventModel> eventSink(ConfigurationManager config) {
        DayEventBucketAssignerTest<EventModel> myBucketAssigner = new DayEventBucketAssignerTest<EventModel>();
        String path = config.getString(Constants.S3_EVENT_PATH);
        StreamingFileSink<EventModel> build4 = StreamingFileSink
                .forBulkFormat(new Path(path),
                        ParquetAvroWriters.forReflectRecord(EventModel.class))
                .withBucketAssigner(myBucketAssigner)
                .build();

        return build4;
    }
}
