package flink.app;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.vdurmont.emoji.EmojiParser;
import flink.assigner.day.DayEventBucketAssignerTest;
import flink.common.ConfigurationManager;
import flink.constant.Constants;
import flink.model.EventModel;
import flink.util.DecryptUtil;
import flink.util.IpUtils;
import flink.util.KafkaUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.core.fs.Path;
import org.apache.flink.formats.parquet.avro.ParquetAvroWriters;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.TimeCharacteristic;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.filesystem.StreamingFileSink;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumerBase;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.lionsoul.ip2region.xdb.Searcher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Properties;
import java.util.TimeZone;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.apache.kafka.clients.producer.ProducerConfig.*;
import static org.apache.kafka.clients.producer.ProducerConfig.TRANSACTION_TIMEOUT_CONFIG;

public class EventsToS3Bak {
    private static Logger log = LoggerFactory.getLogger(EventsToS3Bak.class);
    static Searcher searcher;
    public static void main(String[] args) throws Exception {

//        ConfigurationManager config = new ConfigurationManager(args);

        StreamExecutionEnvironment env = getEnvBak();

        String groupId1 = "EventsToS3BakFix.group1";

//        KafkaSource<JSONObject> sourceWithOffset = getSourceWithOffsetRepair(groupId1);

        KafkaSource<JSONObject> sourceWithOffsetBak = getSourceWithOffsetBak(groupId1);

        DataStreamSource<JSONObject> sensorSource = env.fromSource(sourceWithOffsetBak, WatermarkStrategy.noWatermarks(), "SensorSourceRepair");

//        sensorSource.print("test");
        SingleOutputStreamOperator<EventModel> eventFlatMapStream = sensorSource.flatMap(new RichFlatMapFunction<JSONObject, EventModel>() {
            @Override
            public void flatMap(JSONObject value, Collector<EventModel> out) throws Exception {
                JSONObject body = null;
                Long server_process_time = null;
                String source = null;
                String ip = null;
                try {
                    body = value.getJSONObject("body");
                    JSONObject header = value.getJSONObject("header");
                    ip = header.getString("client_ip") == null ? "未知" : header.getString("client_ip");
                    source = body.getString("source") == null ? "lb-ods-event-topic-r3p3" : value.getString("source");
                    server_process_time = header.getLong("server_process_time")== null? 0: header.getLong("server_process_time");
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
//                    model.Alert.sensorAlert(config.getString(Constants.feishu_URL), "json埋点数据解析异常", value.toString());
                }


                String sensorDataList = null;
                String sensorData = null;
                try {
                    sensorDataList = body.getString("data_list");
                    sensorData = body.getString("data");
                    if(sensorDataList == null && sensorData == null){
                        sensorDataList = body.getString("\"data_list");
                        sensorDataList = sensorDataList.replace("\"", "");
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
                // kafka相关信息获取
                String kafka_offset = value.getString(Constants.KAFKA_OFFSET_KEY);
                String kafka_partition = value.getString(Constants.KAFKA_PARTITION_KEY);
                String kafka_group_id = value.getString(Constants.KAFKA_GROUP_KEY);
                String ip_region = getIpAddress(ip) == null? "未知" : getIpAddress(ip);

                String ip_region_replace = ip_region.replaceAll("0", "");

                String[] region = ip_region_replace.split("\\|");

                String country = null;
                String province = null;
                String city = null;
                try {
                    country = "未知".equals(ip_region)? "未知" : region[0];
                    province = region.length <= 3? "未知" : region[2];
                    city = region.length <= 4? "未知" : region[3];
                } catch (Exception e) {
                    log.error("ip_region : " + ip_region);
                }

                String project = value.getString("project");

        /*
        H5 数据的server_api是sa.gif
        客户端 数据的server_api是sa
         */
                EventModel eventModel = new EventModel();

                if(sensorDataList == null){
                    String resultString = "";
                    JSONObject resultJson = null;
                    // 埋点上报数据解析
                    try {
                        sensorData = EmojiParser.removeAllEmojis(sensorData);
                        sensorData = DecryptUtil.urlDecode(sensorData);
                        try {
                            if(!"1".equals(body.getString("gzip"))) {
                                resultString = DecryptUtil.base64Decode(sensorData);
                            } else {
                                resultString = DecryptUtil.unGzip(sensorData);
                            }
                        } catch (Exception e) {
//                            model.Alert.sensorAlert(config.getString(Constants.feishu_URL), "H5 解压方式异常", value.toString());
                        }
                        resultJson = JSONObject.parseObject(resultString);
                        String distinct_id = resultJson.getString("distinct_id") == null || "".equals(resultJson.getString("distinct_id")) ? "unknown": resultJson.getString("distinct_id");
                        String properties = resultJson.getString("properties") == null || "".equals(resultJson.getString("properties")) ? "unknown": resultJson.getString("properties");
                        String anonymous_id = resultJson.getString("anonymous_id") == null || "".equals(resultJson.getString("anonymous_id")) ? "unknown": resultJson.getString("anonymous_id");
                        String type = resultJson.getString("type") == null || "".equals(resultJson.getString("type")) ? "unknown": resultJson.getString("type");
                        String event = resultJson.getString("event") == null || "".equals(resultJson.getString("event")) ? "unknown": resultJson.getString("event");
                        Long time = resultJson.getLong("time") == null ? 0: resultJson.getLong("time");
                        String track_id = resultJson.getString("_track_id") == null || "".equals(resultJson.getString("_track_id")) ? "unknown": resultJson.getString("_track_id");
                        Long flush_time = resultJson.getLong("_flush_time") == null ? 0: resultJson.getLong("_flush_time");

                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
                        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
                        eventModel.setPt(simpleDateFormat.format(new Date(time)));
                        eventModel.setDistinct_id(distinct_id);
//                eventModel.setProperties(properties);
                        eventModel.setAnonymous_id(anonymous_id);
                        eventModel.setType(type);
                        eventModel.setEvent(event);
                        eventModel.setTrack_id(track_id);
                        eventModel.setFlush_time(flush_time);
                        eventModel.setTime(time);
                        eventModel.setKafka_group_id(kafka_group_id);
                        eventModel.setKafka_offset(kafka_offset);
                        eventModel.setKafka_partition(kafka_partition);
                        eventModel.setProject(project);
                        eventModel.setServer_process_time(server_process_time);
                        eventModel.setKafka_offset_index(kafka_offset + "-0");
                        eventModel.setSource(source);
                        eventModel.setIp(ip);
                        eventModel.setIp_region(ip_region);

                        JSONObject jsonObject = JSONObject.parseObject(properties);
                        jsonObject.put("$country", country);
                        jsonObject.put("$province", province);
                        jsonObject.put("$city", city);
                        jsonObject.put("ip", ip);

                        // 新增字段page_name、 business_type、 platform_type、 account_channel
                        String page_name = jsonObject.getString("page_name") == null || "".equals(jsonObject.getString("page_name"))? "unknown" : jsonObject.getString("page_name") ;
                        String business_type = jsonObject.getString("business_type") == null || "".equals(jsonObject.getString("business_type"))?  "unknown": jsonObject.getString("business_type");
                        String platform_type = jsonObject.getString("platform_type") == null || "".equals(jsonObject.getString("platform_type")) ?  "unknown" : jsonObject.getString("platform_type");
                        String account_channel = jsonObject.getString("account_channel") == null || "".equals(jsonObject.getString("account_channel")) ? "unknown" : jsonObject.getString("account_channel");

                        eventModel.setPage_name(page_name);
                        eventModel.setBusiness_type(business_type);
                        eventModel.setPlatform_type(platform_type);
                        eventModel.setAccount_channel(account_channel);

                        properties = jsonObject.toString();

                        String regexEmoticons = "[\\uD83C-\\uDBFF\\uDC00-\\uDFFF&&[^-]]+";
                        Pattern emoticonPattern = Pattern.compile(regexEmoticons);
                        Matcher emoticonMatcher = emoticonPattern.matcher(properties);

                        // 删除匹配到的 emoji 表情
                        properties = emoticonMatcher.replaceAll("");

                        eventModel.setProperties(properties);

                        out.collect(eventModel);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
//                        model.Alert.sensorAlert(config.getString(Constants.feishu_URL), "data decode 埋点数据异常", value.toString());
                    }
                }
                else if(sensorData == null){
                    // unGzip
                    String resultString = null;
                    sensorDataList = EmojiParser.removeAllEmojis(sensorDataList);
                    sensorDataList = DecryptUtil.urlDecode(sensorDataList);
                    try {
                        if(body.getString("data_list")== null && sensorDataList != null){
                            resultString = DecryptUtil.base64Decode(sensorDataList);
                        }else {
                            resultString = DecryptUtil.unGzip(sensorDataList);
                        }
                    } catch (Exception e) {
                        try {
                            resultString = DecryptUtil.base64Decode(sensorDataList);
                        } catch (Exception ex) {
                            log.error(e.getMessage(), e);
//                            model.Alert.sensorAlert(config.getString(Constants.feishu_URL), "data_list 埋点数据gzip解析异常", String.valueOf(value));
                        }
                    }

                    JSONArray resultArray = JSONArray.parseArray(resultString);
                    if(resultArray == null){
                        resultArray = new JSONArray();
                    }
                    // jsonArray
                    for (int i = 0; i < resultArray.size(); i++){
                        JSONObject resultJson = (JSONObject) resultArray.get(i);

                        try {
                            String distinct_id = resultJson.getString("distinct_id") == null || "".equals(resultJson.getString("distinct_id")) ? "unknown": resultJson.getString("distinct_id");
                            String properties = resultJson.getString("properties") == null || "".equals(resultJson.getString("properties")) ? "unknown": resultJson.getString("properties");
                            String anonymous_id = resultJson.getString("anonymous_id") == null || "".equals(resultJson.getString("anonymous_id")) ? "unknown": resultJson.getString("anonymous_id");
                            String type = resultJson.getString("type") == null || "".equals(resultJson.getString("type")) ? "unknown": resultJson.getString("type");
                            String event = resultJson.getString("event") == null || "".equals(resultJson.getString("event")) ? "unknown": resultJson.getString("event");
                            Long time = resultJson.getLong("time") == null ? 0: resultJson.getLong("time");
                            String track_id = resultJson.getString("_track_id") == null || "".equals(resultJson.getString("_track_id")) ? "unknown": resultJson.getString("_track_id");
                            Long flush_time = resultJson.getLong("_flush_time") == null ? 0: resultJson.getLong("_flush_time");

                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
                            simpleDateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
                            eventModel.setPt(simpleDateFormat.format(new Date(time)));
                            eventModel.setDistinct_id(distinct_id);
                            eventModel.setAnonymous_id(anonymous_id);
                            eventModel.setType(type);
                            eventModel.setEvent(event);
                            eventModel.setTrack_id(track_id);
                            eventModel.setFlush_time(flush_time);
                            eventModel.setTime(time);
                            eventModel.setKafka_group_id(kafka_group_id);
                            eventModel.setKafka_offset(kafka_offset);
                            eventModel.setKafka_partition(kafka_partition);
                            eventModel.setProject(project);
                            eventModel.setServer_process_time(server_process_time);
                            eventModel.setKafka_offset_index(kafka_offset + "-" + (i + 1));
                            eventModel.setSource(source);
                            eventModel.setIp(ip);
                            eventModel.setIp_region(ip_region);


                            JSONObject jsonObject = JSONObject.parseObject(properties);
                            jsonObject.put("$country", country);
                            jsonObject.put("$province", province);
                            jsonObject.put("$city", city);
                            jsonObject.put("ip", ip);

                            // 新增字段page_name、 business_type、 platform_type、 account_channel
                            String page_name = jsonObject.getString("page_name") == null || "".equals(jsonObject.getString("page_name"))? "unknown" : jsonObject.getString("page_name") ;
                            String business_type = jsonObject.getString("business_type") == null || "".equals(jsonObject.getString("business_type"))?  "unknown": jsonObject.getString("business_type");
                            String platform_type = jsonObject.getString("platform_type") == null || "".equals(jsonObject.getString("platform_type")) ?  "unknown" : jsonObject.getString("platform_type");
                            String account_channel = jsonObject.getString("account_channel") == null || "".equals(jsonObject.getString("account_channel")) ? "unknown" : jsonObject.getString("account_channel");

                            eventModel.setPage_name(page_name);
                            eventModel.setBusiness_type(business_type);
                            eventModel.setPlatform_type(platform_type);
                            eventModel.setAccount_channel(account_channel);

                            properties = jsonObject.toString();

                            String regexEmoticons = "[\\uD83C-\\uDBFF\\uDC00-\\uDFFF&&[^-]]+";
                            Pattern emoticonPattern = Pattern.compile(regexEmoticons);
                            Matcher emoticonMatcher = emoticonPattern.matcher(properties);

                            // 删除匹配到的 emoji 表情
                            properties = emoticonMatcher.replaceAll("");

                            eventModel.setProperties(properties);
                            out.collect(eventModel);

                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
//                            model.Alert.sensorAlert(config.getString(Constants.feishu_URL), "data_list 埋点数据解析异常", String.valueOf(value));
                        }
                    }
                }
            }
        });

//        eventFlatMapStream.print("test");

        eventFlatMapStream.map(new MapFunction<EventModel, EventModel>() {
            @Override
            public EventModel map(EventModel eventModel) throws Exception {

                JSONObject jsonObject = JSONObject.parseObject(eventModel.getProperties());
                String accountChannel = eventModel.getAccount_channel();
                String accountChannelProperties = jsonObject.getString("account_channel") == null || "".equals(jsonObject.getString("account_channel")) ? "unknown" : jsonObject.getString("account_channel");
                if(!accountChannel.equalsIgnoreCase(accountChannelProperties)){
                    model.Alert.sensorAlert("https://open.feishu.cn/open-apis/bot/v2/hook/5af95bd0-e82e-43a2-a157-8c6bda0f6330", "数据在计算时异常","accountChannel = " + accountChannel + "\n" + "accountChannelProperties :" + accountChannelProperties);
                }
                return eventModel;
            }
        });

        //发送数据到s3
        StreamingFileSink<EventModel> eventModelStreamingFileSink = eventSink();
        eventFlatMapStream.addSink(eventModelStreamingFileSink);


        env.execute(EventsToS3Bak.class.getSimpleName());
    }

    private static StreamingFileSink<EventModel> eventSink() {
        DayEventBucketAssignerTest<EventModel> myBucketAssigner = new DayEventBucketAssignerTest<EventModel>();
        String path = "oss://lb-bi-event-tracking-log/lb_bi_sensor_event_d_test";
        StreamingFileSink<EventModel> build4 = StreamingFileSink
                .forBulkFormat(new Path(path),
                        ParquetAvroWriters.forReflectRecord(EventModel.class))
                .withBucketAssigner(myBucketAssigner)
                .build();
        return build4;
    }

    public static KafkaSource<JSONObject> getSourceWithOffsetBak(String groupId) {

        Properties propKafka = new Properties();
        propKafka.setProperty(FlinkKafkaConsumerBase.KEY_PARTITION_DISCOVERY_INTERVAL_MILLIS, "10000");
//        InputStream resourceAsStream = sdp2mysql.class.getClassLoader().getResourceAsStream("prod.properties");
//        try {
//            propBasic.load(resourceAsStream);
//        } catch ( IOException e) {
//            System.out.println(e.getMessage());
//            e.printStackTrace();
//        }
        propKafka.setProperty(ACKS_CONFIG, "-1");
        propKafka.setProperty(KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        propKafka.setProperty(VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.ByteArrayDeserializer");

        //RecordAccumulator缓冲区大小32M
        propKafka.setProperty(BUFFER_MEMORY_CONFIG, "33554432");

        //131KB
        propKafka.setProperty(BATCH_SIZE_CONFIG, "131072");

        //100ms
        propKafka.setProperty(LINGER_MS_CONFIG, "100");

        propKafka.setProperty(MAX_REQUEST_SIZE_CONFIG, "83886080");
        propKafka.setProperty(RETRIES_CONFIG, "10");
        propKafka.setProperty(RETRY_BACKOFF_MS_CONFIG, "500");
        propKafka.setProperty(TRANSACTION_TIMEOUT_CONFIG, 60 * 5 * 1000 + "");

        // b-3.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092,b-2.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092,b-1.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092
        propKafka.setProperty(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, "alikafka-post-public-intl-sg-uz63j8tfo01-1-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-uz63j8tfo01-2-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-uz63j8tfo01-3-vpc.alikafka.aliyuncs.com:9092");
        propKafka.setProperty(ConsumerConfig.GROUP_ID_CONFIG, "EventsToS3Bak.group");
        propKafka.setProperty(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
//        propKafka.setProperty(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");


        return KafkaSource.<JSONObject>builder()
                .setBootstrapServers("alikafka-post-public-intl-sg-uz63j8tfo01-1-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-uz63j8tfo01-2-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-uz63j8tfo01-3-vpc.alikafka.aliyuncs.com:9092")
                .setTopics("pub-test-topic-r3p3")
                .setGroupId(groupId)
                .setProperties(propKafka)
//                .setStartingOffsets(OffsetsInitializer.latest())
//                .setStartingOffsets(OffsetsInitializer.timestamp(1711821487000l))
                .setDeserializer(new KafkaRecordDeserializationSchema<JSONObject>() {
                                     @Override
                                     public TypeInformation<JSONObject> getProducedType() {
                                         return TypeInformation.of(JSONObject.class);
                                     }
                                     @Override
                                     public void deserialize(ConsumerRecord<byte[], byte[]> record, Collector<JSONObject> out) throws IOException {
                                         String jsonString = new String(record.value(), StandardCharsets.UTF_8);
                                         try {
                                             JSONObject jsonObject = JSONObject.parseObject(jsonString);
                                             jsonObject.put(Constants.KAFKA_OFFSET_KEY, record.offset());
                                             jsonObject.put(Constants.KAFKA_PARTITION_KEY, record.partition());
                                             jsonObject.put(Constants.KAFKA_GROUP_KEY, "EventsToS3Bak.group");
                                             out.collect(jsonObject);
                                         } catch (Exception e) {
                                             System.out.println(e.getMessage());
                                         }
                                     }
                                 }
                ).build();
    }

    public static StreamExecutionEnvironment getEnvBak() throws IOException {
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setStreamTimeCharacteristic(TimeCharacteristic.ProcessingTime);
        String myEnv = "prod";
        CheckpointConfig checkpointConfig = env.getCheckpointConfig();
        checkpointConfig.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        checkpointConfig.setTolerableCheckpointFailureNumber(12);
        checkpointConfig.setCheckpointTimeout(30000L);
        checkpointConfig.enableExternalizedCheckpoints(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        RestartStrategies.FailureRateRestartStrategyConfiguration failureRateRestartStrategyConfiguration =
                RestartStrategies.failureRateRestart(
                        10,
                        org.apache.flink.api.common.time.Time.minutes(10),
                        org.apache.flink.api.common.time.Time.seconds(15));
        env.setRestartStrategy(failureRateRestartStrategyConfiguration);
        env.enableCheckpointing(300000L);
        return env;
    }

    public static String getIpAddress(String ip){
        if ("127.0.0.1".equals(ip) || ip.startsWith("192.168")) {
            return "局域网 ip";
        }
        if (searcher == null) {
            try {
                String dbPath = IpUtils.createFtlFileByFtlArray();
                searcher = Searcher.newWithFileOnly(dbPath);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        String region = null;
        String errorMessage = null;
        try {
            region = searcher.search(ip);
        } catch (Exception e) {
            errorMessage = e.getMessage();
            if (errorMessage != null && errorMessage.length() > 256) {
                errorMessage = errorMessage.substring(0,256);
            }
            e.printStackTrace();
        }
        // 输出 region
        return region;
    }


    public static KafkaSource<JSONObject> getSourceWithOffsetRepair(String groupId) {
        Properties propKafka = new Properties();
        propKafka.setProperty(FlinkKafkaConsumerBase.KEY_PARTITION_DISCOVERY_INTERVAL_MILLIS, "10000");
//        InputStream resourceAsStream = sdp2mysql.class.getClassLoader().getResourceAsStream("prod.properties");
//        try {
//            propBasic.load(resourceAsStream);
//        } catch ( IOException e) {
//            System.out.println(e.getMessage());
//            e.printStackTrace();
//        }
        propKafka.setProperty(ACKS_CONFIG, "-1");
        propKafka.setProperty(KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        propKafka.setProperty(VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.ByteArrayDeserializer");

        //RecordAccumulator缓冲区大小32M
        propKafka.setProperty(BUFFER_MEMORY_CONFIG, "33554432");

        //131KB
        propKafka.setProperty(BATCH_SIZE_CONFIG, "131072");

        //100ms
        propKafka.setProperty(LINGER_MS_CONFIG, "100");

        propKafka.setProperty(MAX_REQUEST_SIZE_CONFIG, "83886080");
        propKafka.setProperty(RETRIES_CONFIG, "10");
        propKafka.setProperty(RETRY_BACKOFF_MS_CONFIG, "500");
        propKafka.setProperty(TRANSACTION_TIMEOUT_CONFIG, 60 * 5 * 1000 + "");

        // b-3.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092,b-2.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092,b-1.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092
        propKafka.setProperty(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, "alikafka-post-public-intl-sg-uz63j8tfo01-1-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-uz63j8tfo01-2-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-uz63j8tfo01-3-vpc.alikafka.aliyuncs.com:9092");
        propKafka.setProperty(ConsumerConfig.GROUP_ID_CONFIG, "EventsToS3Bak.group");
//        propKafka.setProperty(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
//        prop.setProperty("max.partition.fetch.bytes", "52428800");
//        prop.setProperty("fetch.message.max.bytes", "52428800");
        return KafkaSource.<JSONObject>builder()
                .setBootstrapServers("alikafka-post-public-intl-sg-uz63j8tfo01-1-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-uz63j8tfo01-2-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-uz63j8tfo01-3-vpc.alikafka.aliyuncs.com:9092")
                .setTopics("pub-test-topic-r3p3")
                .setGroupId(groupId)
                .setProperties(propKafka)
                .setStartingOffsets(OffsetsInitializer.latest())
                .setDeserializer(new KafkaRecordDeserializationSchema<JSONObject>() {
                                     @Override
                                     public TypeInformation<JSONObject> getProducedType() {
                                         return TypeInformation.of(JSONObject.class);
                                     }
                                     @Override
                                     public void deserialize(ConsumerRecord<byte[], byte[]> record, Collector<JSONObject> out) throws IOException {
                                         String jsonString = new String(record.value(), StandardCharsets.UTF_8);
                                         try {
                                             JSONObject jsonObject = JSONObject.parseObject(jsonString);
                                             out.collect(jsonObject);
                                         } catch (Exception e) {
                                             log.error(e.getMessage(), e);
                                             model.Alert.sensorAlert("https://open.feishu.cn/open-apis/bot/v2/hook/5af95bd0-e82e-43a2-a157-8c6bda0f6330", "json新增offset异常报错", "KAFKA_OFFSET_KEY : " + record.offset());
                                         }
                                     }
                                 }
                ).build();
    }
}
