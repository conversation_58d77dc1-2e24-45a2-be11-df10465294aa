package flink.app;

import com.alibaba.fastjson.JSONObject;
import flink.assigner.day.DayEventBucketAssigner;
import flink.common.ConfigurationManager;
import flink.constant.Constants;
import flink.model.EventModel;
import flink.util.BaseUtil;
import flink.util.KafkaUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.api.connector.sink.Sink;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.core.fs.Path;
import org.apache.flink.formats.parquet.avro.ParquetAvroWriters;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.api.functions.sink.filesystem.StreamingFileSink;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumer;
import org.apache.flink.util.Collector;
import org.apache.twill.kafka.client.KafkaConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;
import java.util.regex.Pattern;

public class OffsetToNew {
    private static Logger log = LoggerFactory.getLogger(OffsetToNew.class);
    public static void main(String[] args) throws Exception {

        ConfigurationManager config = new ConfigurationManager(args);

        StreamExecutionEnvironment env = BaseUtil.getEnv(config);

        Properties prop = KafkaUtils.initProp(config);

        FlinkKafkaConsumer<String> membersKafkaConsumer = new FlinkKafkaConsumer<>(
                config.getString(Constants.KAFKA_EVENT_SOURCE_TOPIC),
                new SimpleStringSchema(),
                prop
        );

        membersKafkaConsumer.setStartFromEarliest();
        DataStreamSource<String> offsetStream = env.addSource(membersKafkaConsumer);

        KafkaSink kafkaSink = KafkaUtils.KafkaSinkExactlyOnce(config);

        offsetStream.process(new ProcessFunction<String, String>() {
            @Override
            public void processElement(String s, ProcessFunction<String, String>.Context context, Collector<String> collector) throws Exception {
                log.error("offset ： " + s);
            }
        });

//        sensorSource.print("test");
//        SingleOutputStreamOperator<EventModel> eventFlatMapStream = sensorSource.flatMap(new EventFlatMap());

//        eventFlatMapStream.print("test");

        //发送数据到s3
//        StreamingFileSink<EventModel> eventModelStreamingFileSink = eventSink(config);
//        eventFlatMapStream.addSink(eventModelStreamingFileSink);

//        //发送数据到kafka
//        KafkaSink kafkaSink = KafkaUtils.KafkaSinkExactlyOnce(config, Constants.KAFKA_EVENT_SINK_TOPIC);
//        eventFlatMapStream.map(data -> data.toJson().toString()).sinkTo(kafkaSink);

        offsetStream.sinkTo(kafkaSink);

        env.execute(OffsetToNew.class.getSimpleName());
    }

//    private static StreamingFileSink<EventModel> eventSink(ConfigurationManager config) {
//        DayEventBucketAssigner<EventModel> myBucketAssigner = new DayEventBucketAssigner<>();
//        String path = config.getString(Constants.S3_EVENT_PATH);
//        StreamingFileSink<EventModel> build4 = StreamingFileSink
//                .forBulkFormat(new Path(path),
//                        ParquetAvroWriters.forReflectRecord(EventModel.class))
//                .withBucketAssigner(myBucketAssigner)
//                .build();
//        return build4;
//    }

}
