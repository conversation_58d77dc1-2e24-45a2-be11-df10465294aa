package flink.app;

import com.alibaba.fastjson.JSONObject;
import flink.assigner.day.DayEventBucketAssigner;
import flink.common.ConfigurationManager;
import flink.constant.Constants;
import flink.function.EventFlatMap;
import flink.model.EventModel;
import flink.util.BaseUtil;
import flink.util.KafkaUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.core.fs.Path;
import org.apache.flink.formats.parquet.avro.ParquetAvroWriters;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.api.functions.sink.filesystem.StreamingFileSink;
import org.apache.flink.streaming.connectors.kafka.partitioner.FlinkKafkaPartitioner;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Properties;
import java.util.UUID;

import static flink.util.KafkaUtils.initProp;

public class SensorAnalyzeToKafka {
    private static Logger log = LoggerFactory.getLogger(SensorAnalyzeToKafka.class);
    public static void main(String[] args) throws Exception {
        ConfigurationManager config = new ConfigurationManager(args);
        StreamExecutionEnvironment env = BaseUtil.getEnv(config);

        KafkaSource<JSONObject> eventModelFlinkKafkaConsumer = getSourceWithOffsetEarliest(config);

//        KafkaSource<JSONObject> sourceWithOffset = KafkaUtils.getSourceWithOffsetRepair(config);

        // 神策修复数据
//        DataStreamSource<JSONObject> sensorSourceRepair = env.fromSource(sourceWithOffset, WatermarkStrategy.noWatermarks(), "SensorSourceRepair");

        // 正常神策数据
        DataStreamSource<JSONObject> sensorSourceResult = env.fromSource(eventModelFlinkKafkaConsumer, WatermarkStrategy.noWatermarks(), "SensorSource");


        // 合并工作流
//        DataStream<JSONObject> sensorSourceResult = sensorSource.union(sensorSourceRepair);

        // 解析神策数据
        SingleOutputStreamOperator<EventModel> eventFlatMapStream = sensorSourceResult.flatMap(new EventFlatMap(config));

        OutputTag<EventModel> kafkaDataTag = new OutputTag<EventModel>("kafkaDataTag"){};

        SingleOutputStreamOperator<EventModel> S3DataResultStream = eventFlatMapStream.process(new ProcessFunction<EventModel, EventModel>() {
            @Override
            public void processElement(EventModel eventModel, ProcessFunction<EventModel, EventModel>.Context context, Collector<EventModel> collector) throws Exception {
                EventModel eventModelKafka = eventModel;
                context.output(kafkaDataTag, eventModelKafka);
                if(eventModel.getEvent() != null && eventModel.getType().contains("track")){
                    try {
                        collector.collect(eventModel);
                    } catch (Exception e) {
                        log.error("eventModel : " + eventModel);
                        throw new RuntimeException(e);
                    }
                }
            }
        });

        DataStream<EventModel> KafkaDataResultStream = S3DataResultStream.getSideOutput(kafkaDataTag);

        //发送数据到s3
//        StreamingFileSink<EventModel> eventModelStreamingFileSink = eventSink(config);
//        S3DataResultStream.addSink(eventModelStreamingFileSink);

        //发送数据到kafka
        KafkaSink kafkaSink = KafkaSinkExactlyOnceTest(config);
        KafkaDataResultStream.map(data -> data.toJson().toString()).sinkTo(kafkaSink);

        env.execute(SensorAnalyzeToKafka.class.getSimpleName());
    }

    private static StreamingFileSink<EventModel> eventSink(ConfigurationManager config) {
        DayEventBucketAssigner<EventModel> myBucketAssigner = new DayEventBucketAssigner<>();
        String path = config.getString(Constants.S3_EVENT_PATH);
        StreamingFileSink<EventModel> build4 = StreamingFileSink
                .forBulkFormat(new Path(path),
                        ParquetAvroWriters.forReflectRecord(EventModel.class))
                .withBucketAssigner(myBucketAssigner)
//                .withRollingPolicy()
                .withBucketCheckInterval(1000)
                .build();
        return build4;
    }

    private static KafkaSource<JSONObject> getSourceWithOffsetEarliest(ConfigurationManager config) {
        Properties prop = initProp(config);
        return KafkaSource.<JSONObject>builder()
                .setBootstrapServers("alikafka-post-public-intl-sg-uz63j8tfo01-1-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-uz63j8tfo01-2-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-uz63j8tfo01-3-vpc.alikafka.aliyuncs.com:9092")
                .setTopics("pub-ods-undecoded-event-r3p3")
                .setGroupId("SensorAnalyzeToKafka-group")
                .setProperties(prop)
                .setStartingOffsets(OffsetsInitializer.earliest())
                .setDeserializer(new KafkaRecordDeserializationSchema<JSONObject>() {
                                     @Override
                                     public TypeInformation<JSONObject> getProducedType() {
                                         return TypeInformation.of(JSONObject.class);
                                     }
                                     @Override
                                     public void deserialize(ConsumerRecord<byte[], byte[]> record, Collector<JSONObject> out) throws IOException {
                                         String jsonString = new String(record.value(), StandardCharsets.UTF_8);
                                         try {
                                             JSONObject jsonObject = JSONObject.parseObject(jsonString);
                                             jsonObject.put(Constants.KAFKA_OFFSET_KEY, record.offset());
                                             jsonObject.put(Constants.KAFKA_PARTITION_KEY, record.partition());
                                             jsonObject.put(Constants.KAFKA_GROUP_KEY, config.getString(Constants.KAFKA_EVENT_GROUP));
                                             out.collect(jsonObject);
                                         } catch (Exception e) {
                                             log.error(e.getMessage(), e);
                                             model.Alert.sensorAlert(config.getString(Constants.feishu_URL), "json新增offset异常报错-test", "KAFKA_OFFSET_KEY : " + record.offset());
                                         }
                                     }
                                 }
                ).build();
    }

    public static KafkaSink KafkaSinkExactlyOnceTest(ConfigurationManager config) {
        Properties prop = new Properties();
        UUID transactionID = UUID.randomUUID();
        prop.setProperty("transaction.timeout.ms", "900000");
        KafkaSink<String> sink = KafkaSink.<String>builder()
                .setKafkaProducerConfig(prop)
                .setBootstrapServers("alikafka-post-public-intl-sg-uz63j8tfo01-1-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-uz63j8tfo01-2-vpc.alikafka.aliyuncs.com:9092,alikafka-post-public-intl-sg-uz63j8tfo01-3-vpc.alikafka.aliyuncs.com:9092")
                .setRecordSerializer(KafkaRecordSerializationSchema.builder()
                                .setTopic("pub-test-topic-r3p3")
                                .setPartitioner(new FlinkKafkaPartitioner<String>() {
                                    @Override
                                    public int partition(String record, byte[] key, byte[] value, String targetTopic, int[] partitions) {
                                        JSONObject data = JSONObject.parseObject(record);
                                        String distinctId = data.getString("distinct_id") == null ? "1" : data.getString("distinct_id");
//                                String distinctId = record.getDistinct_id();
                                        return Math.abs(distinctId.hashCode()) % partitions.length;
                                    }
                                })
                                .setValueSerializationSchema(new SimpleStringSchema())
                                .build()
                )
                .setDeliverGuarantee(DeliveryGuarantee.EXACTLY_ONCE)
//                .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                .setTransactionalIdPrefix(transactionID.toString())
                .build();


        return sink;
    }

}
