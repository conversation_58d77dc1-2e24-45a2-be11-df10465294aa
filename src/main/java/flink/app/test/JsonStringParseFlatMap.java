package flink.app.test;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import org.apache.flink.api.common.functions.FlatMapFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;

/**
 * 将json字符串数据转为JsonObject
 * 转换失败捕获异常，记录日志，避免程序因异常数据终止
 *
 * <AUTHOR>
 * @date 2022/6/30 3:10 下午
 */
public class JsonStringParseFlatMap implements FlatMapFunction<String, JSONObject> {

    private final Logger log;

    public JsonStringParseFlatMap(Logger log) {
        this.log = log;
    }


    @Override
    public void flatMap(String value, Collector<JSONObject> out) throws Exception {
        try {
            JSONObject jsonObject = JSONObject.parseObject(value);
            out.collect(jsonObject);
        } catch (JSONException e) {
            log.error(value);
            log.error(e.getMessage(), e);
        }
    }
}
