package flink.app.test;

import com.alibaba.fastjson.JSONObject;
import flink.assigner.day.DayEventBucketAssignerTest;
import flink.constant.Constants;
import flink.model.EventModel;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.core.fs.Path;
import org.apache.flink.formats.parquet.avro.ParquetAvroWriters;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.TimeCharacteristic;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.filesystem.StreamingFileSink;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumerBase;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.lionsoul.ip2region.xdb.Searcher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.apache.kafka.clients.producer.ProducerConfig.*;

public class SensorToOSSDemo {
    private static Logger log = LoggerFactory.getLogger(SensorToOSSDemo.class);
    static Searcher searcher;
    public static void main(String[] args) throws Exception {
        StreamExecutionEnvironment env = getEnvBak();

        ArrayList<String> DataCollection = new ArrayList<String>();

        String data1 = "{\"kafka_partition\":\"2-3\",\"kafka_offset_index\":\"483375-0-3\",\"server_process_time\":*************,\"pt\":\"********\",\"project\":\"production-3\",\"flush_time\":*************,\"kafka_group_id\":\"EventsToS3staginggroup-3\",\"source\":\"lb-ods-event-topic-r3p8-3\",\"type\":\"track-3\",\"kafka_offset\":\"483375-3\",\"platform_type\":\"web-3\",\"distinct_id\":\"333333-3\",\"track_id\":\"*********-3\",\"anonymous_id\":\"184c798822d15f-01df0181e2f9ec-********-5089536-384c798822e1ad4-3\",\"page_name\":\"unknown-3\",\"business_type\":\"unknown-3\",\"time\":*************,\"event\":\"$pageview-3\",\"account_channel\":\"unknown-3\",\"properties\":{\"$is_first_day\":false,\"$referrer_host\":\"longportapp.com-3\",\"$screen_width\":3008,\"$is_first_time\":false,\"$screen_height\":1692,\"$referrer\":\"https://longportapp.com/zh-CN/topics/********-3\",\"$lib\":\"js-3\",\"$url_path\":\"/zh-CN/quote/FFIE.US-3\",\"platform_type\":\"web-3\",\"org_id\":\"-3\",\"$title\":\"null-3\",\"$timezone_offset\":-480,\"$lib_version\":\"1.16.10-3\",\"$latest_search_keyword\":\"未取到值_直接打开-3\",\"$latest_traffic_source_type\":\"直接流量-3\",\"$url\":\"https://longportapp.com/zh-CN/quote/FFIE.US-3\",\"$latest_referrer\":\"-3\",\"account_channel\":\"333333-3\",\"user_agent\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}";
        String data2 = "{\"kafka_partition\":\"2-4\",\"kafka_offset_index\":\"483375-0-4\",\"server_process_time\":*************,\"pt\":\"********\",\"project\":\"production-4\",\"flush_time\":*************,\"kafka_group_id\":\"EventsToS3staginggroup-4\",\"source\":\"lb-ods-event-topic-r3p8-4\",\"type\":\"track-4\",\"kafka_offset\":\"483375-4\",\"platform_type\":\"web-4\",\"distinct_id\":\"333333-4\",\"track_id\":\"*********-4\",\"anonymous_id\":\"184c798822d15f-01df0181e2f9ec-********-5089536-484c798822e1ad4-4\",\"page_name\":\"unknown-4\",\"business_type\":\"unknown-4\",\"time\":*************,\"event\":\"$pageview-4\",\"account_channel\":\"unknown-4\",\"properties\":{\"$is_first_day\":false,\"$referrer_host\":\"longportapp.com-4\",\"$screen_width\":3008,\"$is_first_time\":false,\"$screen_height\":1692,\"$referrer\":\"https://longportapp.com/zh-CN/topics/********-4\",\"$lib\":\"js-4\",\"$url_path\":\"/zh-CN/quote/FFIE.US-4\",\"platform_type\":\"web-4\",\"org_id\":\"-4\",\"$title\":\"null-4\",\"$timezone_offset\":-480,\"$lib_version\":\"1.16.10-4\",\"$latest_search_keyword\":\"未取到值_直接打开-4\",\"$latest_traffic_source_type\":\"直接流量-4\",\"$url\":\"https://longportapp.com/zh-CN/quote/FFIE.US-4\",\"$latest_referrer\":\"-4\",\"account_channel\":\"333333-4\",\"user_agent\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}";
        String data3 = "{\"kafka_partition\":\"2\",\"kafka_offset_index\":\"483375-0\",\"server_process_time\":*************,\"pt\":\"********\",\"project\":\"production\",\"flush_time\":*************,\"kafka_group_id\":\"EventsToS3staginggroup\",\"source\":\"lb-ods-event-topic-r3p8\",\"type\":\"track\",\"kafka_offset\":\"483375\",\"platform_type\":\"web\",\"distinct_id\":\"1111111\",\"track_id\":\"*********\",\"anonymous_id\":\"184c798822d15f-01df0181e2f9ec-********-5089536-184c798822e1ad4\",\"page_name\":\"unknown\",\"business_type\":\"unknown\",\"time\":*************,\"event\":\"$pageview\",\"account_channel\":\"unknown\",\"properties\":{\"$is_first_day\":false,\"$referrer_host\":\"longportapp.com\",\"$screen_width\":3008,\"$is_first_time\":false,\"$screen_height\":1692,\"$referrer\":\"https://longportapp.com/zh-CN/topics/********\",\"$lib\":\"js\",\"$url_path\":\"/zh-CN/quote/FFIE.US\",\"platform_type\":\"web\",\"org_id\":\"\",\"$title\":\"$法拉第未来.US To the moonhis is a message -- with 😀 \ud83c emoji!\uD83D\uDE80\uD83D\uDE80\uD83D\uDE80\uD83D\uDE80\uD83D\uDE80\uD83D\uDE80\uD83D\uDE80\uD83D\uDE80\uD83D\uDE80\uD83D\uDE80\uD83D\uDE80\uD83D\uDE80\uD83D\uDE80\uD83D\uDE80? - LongPort\",\"$timezone_offset\":-480,\"$lib_version\":\"1.16.10\",\"$latest_search_keyword\":\"未取到值_直接打开\",\"$latest_traffic_source_type\":\"直接流量\",\"$url\":\"https://longportapp.com/zh-CN/quote/FFIE.US\",\"$latest_referrer\":\"\",\"account_channel\":\"\",\"user_agent\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}";
        String data4 = "{\"kafka_partition\":\"2-1\",\"kafka_offset_index\":\"483375-0-1\",\"server_process_time\":*************,\"pt\":\"********\",\"project\":\"production-1\",\"flush_time\":*************,\"kafka_group_id\":\"EventsToS3staginggroup-1\",\"source\":\"lb-ods-event-topic-r3p8-1\",\"type\":\"track-1\",\"kafka_offset\":\"483375-1\",\"platform_type\":\"web-1\",\"distinct_id\":\"333333-1\",\"track_id\":\"*********-1\",\"anonymous_id\":\"184c798822d15f-01df0181e2f9ec-********-5089536-184c798822e1ad4-1\",\"page_name\":\"unknown-1\",\"business_type\":\"unknown-1\",\"time\":*************,\"event\":\"$pageview-1\",\"account_channel\":\"unknown-1\",\"properties\":{\"$is_first_day\":false,\"$referrer_host\":\"longportapp.com-1\",\"$screen_width\":3008,\"$is_first_time\":false,\"$screen_height\":1692,\"$referrer\":\"https://longportapp.com/zh-CN/topics/********-1\",\"$lib\":\"js-1\",\"$url_path\":\"/zh-CN/quote/FFIE.US-1\",\"platform_type\":\"web-1\",\"org_id\":\"-1\",\"$title\":\"null-1\",\"$timezone_offset\":-480,\"$lib_version\":\"1.16.10-1\",\"$latest_search_keyword\":\"未取到值_直接打开-1\",\"$latest_traffic_source_type\":\"直接流量-1\",\"$url\":\"https://longportapp.com/zh-CN/quote/FFIE.US-1\",\"$latest_referrer\":\"-1\",\"account_channel\":\"333333-1\",\"user_agent\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}";
        String data5 = "{\"kafka_partition\":\"2-2\",\"kafka_offset_index\":\"483375-0-2\",\"server_process_time\":*************,\"pt\":\"********\",\"project\":\"production-2\",\"flush_time\":*************,\"kafka_group_id\":\"EventsToS3staginggroup-2\",\"source\":\"lb-ods-event-topic-r3p8-2\",\"type\":\"track-2\",\"kafka_offset\":\"483375-2\",\"platform_type\":\"web-2\",\"distinct_id\":\"333333-2\",\"track_id\":\"*********-2\",\"anonymous_id\":\"184c798822d15f-01df0181e2f9ec-********-5089536-284c798822e1ad4-2\",\"page_name\":\"unknown-2\",\"business_type\":\"unknown-2\",\"time\":*************,\"event\":\"$pageview-2\",\"account_channel\":\"unknown-2\",\"properties\":{\"$is_first_day\":false,\"$referrer_host\":\"longportapp.com-2\",\"$screen_width\":3008,\"$is_first_time\":false,\"$screen_height\":1692,\"$referrer\":\"https://longportapp.com/zh-CN/topics/********-2\",\"$lib\":\"js-2\",\"$url_path\":\"/zh-CN/quote/FFIE.US-2\",\"platform_type\":\"web-2\",\"org_id\":\"-2\",\"$title\":\"null-2\",\"$timezone_offset\":-480,\"$lib_version\":\"1.16.10-2\",\"$latest_search_keyword\":\"未取到值_直接打开-2\",\"$latest_traffic_source_type\":\"直接流量-2\",\"$url\":\"https://longportapp.com/zh-CN/quote/FFIE.US-2\",\"$latest_referrer\":\"-2\",\"account_channel\":\"333333-2\",\"user_agent\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"}}";

        byte[] bytes_1 = data1.getBytes(StandardCharsets.UTF_8);
        String result_1 = new String(bytes_1, StandardCharsets.UTF_8);
        byte[] bytes_2 = data2.getBytes(StandardCharsets.UTF_8);
        String result_2 = new String(bytes_2, StandardCharsets.UTF_8);
        byte[] bytes_3 = data3.getBytes(StandardCharsets.UTF_8);
        String result_3 = new String(bytes_3, StandardCharsets.UTF_8);
        byte[] bytes_4 = data4.getBytes(StandardCharsets.UTF_8);
        String result_4 = new String(bytes_4, StandardCharsets.UTF_8);
        byte[] bytes_5 = data5.getBytes(StandardCharsets.UTF_8);
        String result_5 = new String(bytes_5, StandardCharsets.UTF_8);

        DataCollection.add(result_1);
        DataCollection.add(result_2);
        DataCollection.add(result_3);
        DataCollection.add(result_4);
        DataCollection.add(result_5);


        int i = 1;
        while (i <= 10000){
            DataCollection.add(result_2);
            i++;
            if(i % 10 == 0){
                DataCollection.add(result_3);
            }
        }

        DataStreamSource<String> sensorSource = env.fromCollection(DataCollection);

        SingleOutputStreamOperator<JSONObject> resultStream = sensorSource
                .map(data -> JSONObject.parseObject(data));

        SingleOutputStreamOperator<EventModel> eventFlatMapStream = resultStream.flatMap(new RichFlatMapFunction<JSONObject, EventModel>() {
            @Override
            public void flatMap(JSONObject value, Collector<EventModel> out) throws Exception {
                JSONObject body = null;
                Long server_process_time = null;
                String source = "未知";
                server_process_time = value.getLong("server_process_time")== null? 0: value.getLong("server_process_time");
                // kafka相关信息获取
//                String kafka_offset = value.getString(Constants.KAFKA_OFFSET_KEY);
//                String kafka_partition = value.getString(Constants.KAFKA_PARTITION_KEY);
//                String kafka_group_id = value.getString(Constants.KAFKA_GROUP_KEY);

                String kafka_offset = "未知";
                String kafka_partition = "未知";
                String kafka_group_id = "未知";
                String ip = "未知";
                String ip_region = "未知";
                String country = "未知";
                String province = "未知";
                String city = "未知";
                String project = value.getString("project");

                EventModel eventModel = new EventModel();

                JSONObject resultJson = value;
                // 埋点上报数据解析
                try {
                    String distinct_id = resultJson.getString("distinct_id") == null || "".equals(resultJson.getString("distinct_id")) ? "unknown": resultJson.getString("distinct_id");
                    String properties = resultJson.getString("properties") == null || "".equals(resultJson.getString("properties")) ? "unknown": resultJson.getString("properties");
                    String anonymous_id = resultJson.getString("anonymous_id") == null || "".equals(resultJson.getString("anonymous_id")) ? "unknown": resultJson.getString("anonymous_id");
                    String type = resultJson.getString("type") == null || "".equals(resultJson.getString("type")) ? "unknown": resultJson.getString("type");
                    String event = resultJson.getString("event") == null || "".equals(resultJson.getString("event")) ? "unknown": resultJson.getString("event");
                    Long time = resultJson.getLong("time") == null ? 0: resultJson.getLong("time");
                    String track_id = resultJson.getString("_track_id") == null || "".equals(resultJson.getString("_track_id")) ? "unknown": resultJson.getString("_track_id");
                    Long flush_time = resultJson.getLong("_flush_time") == null ? 0: resultJson.getLong("_flush_time");
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
                    simpleDateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));

                    eventModel.setPt(simpleDateFormat.format(new Date(time)));
                    eventModel.setDistinct_id(distinct_id);
                    eventModel.setAnonymous_id(anonymous_id);
                    eventModel.setType(type);
                    eventModel.setEvent(event);
                    eventModel.setTrack_id(track_id);
                    eventModel.setFlush_time(flush_time);
                    eventModel.setTime(time);
                    eventModel.setKafka_group_id(kafka_group_id);
                    eventModel.setKafka_offset(kafka_offset);
                    eventModel.setKafka_partition(kafka_partition);
                    eventModel.setProject(project);
                    eventModel.setServer_process_time(server_process_time);
                    eventModel.setKafka_offset_index(kafka_offset + "-0");
                    eventModel.setSource(source);
                    eventModel.setIp(ip);
                    eventModel.setIp_region(ip_region);
                    JSONObject jsonObject = JSONObject.parseObject(properties);
                    jsonObject.put("$country", country);
                    jsonObject.put("$province", province);
                    jsonObject.put("$city", city);
                    jsonObject.put("ip", ip);
                    // 新增字段page_name、 business_type、 platform_type、 account_channel
                    String page_name = jsonObject.getString("page_name") == null || "".equals(jsonObject.getString("page_name"))? "unknown" : jsonObject.getString("page_name") ;
                    String business_type = jsonObject.getString("business_type") == null || "".equals(jsonObject.getString("business_type"))?  "unknown": jsonObject.getString("business_type");
                    String platform_type = jsonObject.getString("platform_type") == null || "".equals(jsonObject.getString("platform_type")) ?  "unknown" : jsonObject.getString("platform_type");
                    String account_channel = jsonObject.getString("account_channel") == null || "".equals(jsonObject.getString("account_channel")) ? "unknown" : jsonObject.getString("account_channel");
                    eventModel.setPage_name(page_name);
                    eventModel.setBusiness_type(business_type);
                    eventModel.setPlatform_type(platform_type);
                    eventModel.setAccount_channel(account_channel);
                    properties = jsonObject.toString();

                    eventModel.setProperties(properties);
                    out.collect(eventModel);

                } catch (Exception e) {
                    log.error(e.getMessage(), e);
//                    log.error("eventModel :" + eventModel.toJson());
                    log.error("eventModel :" + eventModel.toString());
                }



            }
        });

        //发送数据到s3
        StreamingFileSink<EventModel> eventModelStreamingFileSink = eventSink();
        eventFlatMapStream.addSink(eventModelStreamingFileSink);


        env.execute(SensorToOSSDemo.class.getSimpleName());
    }
    private static StreamingFileSink<EventModel> eventSink() {
        DayEventBucketAssignerTest<EventModel> myBucketAssigner = new DayEventBucketAssignerTest<>();
        String path = "oss://canary-lb-bi-event-tracking-log/lb_bi_sensor_event_d_test";
        StreamingFileSink<EventModel> build4 = StreamingFileSink
                .forBulkFormat(new Path(path),
                        ParquetAvroWriters.forReflectRecord(EventModel.class))
                .withBucketAssigner(myBucketAssigner)
                .withBucketCheckInterval(100l)
                .build();
        return build4;
    }

    public static StreamExecutionEnvironment getEnvBak() throws IOException {
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setStreamTimeCharacteristic(TimeCharacteristic.ProcessingTime);
        String myEnv = "staging";
        CheckpointConfig checkpointConfig = env.getCheckpointConfig();
        checkpointConfig.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        checkpointConfig.setTolerableCheckpointFailureNumber(12);
        checkpointConfig.setCheckpointTimeout(1000L);
        checkpointConfig.enableExternalizedCheckpoints(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        RestartStrategies.FailureRateRestartStrategyConfiguration failureRateRestartStrategyConfiguration =
                RestartStrategies.failureRateRestart(
                        10,
                        org.apache.flink.api.common.time.Time.minutes(10),
                        org.apache.flink.api.common.time.Time.seconds(15));
        env.setRestartStrategy(failureRateRestartStrategyConfiguration);
        env.enableCheckpointing(100L);
        return env;
    }


    public static Properties initProp() {
        Properties propKafka = new Properties();
        propKafka.setProperty(FlinkKafkaConsumerBase.KEY_PARTITION_DISCOVERY_INTERVAL_MILLIS, "10000");

        propKafka.setProperty(ACKS_CONFIG, "-1");
        propKafka.setProperty(KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        propKafka.setProperty(VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.ByteArrayDeserializer");
        //RecordAccumulator缓冲区大小32M
        propKafka.setProperty(BUFFER_MEMORY_CONFIG, "33554432");
        //131KB
        propKafka.setProperty(BATCH_SIZE_CONFIG, "131072");
        //100ms
        propKafka.setProperty(LINGER_MS_CONFIG, "100");
        propKafka.setProperty(MAX_REQUEST_SIZE_CONFIG, "83886080");
        propKafka.setProperty(RETRIES_CONFIG, "10");
        propKafka.setProperty(RETRY_BACKOFF_MS_CONFIG, "500");
        propKafka.setProperty(TRANSACTION_TIMEOUT_CONFIG, 60 * 5 * 1000 + "");
        propKafka.setProperty(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        return propKafka;
    }
}