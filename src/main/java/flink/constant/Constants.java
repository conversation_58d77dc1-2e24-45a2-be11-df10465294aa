package flink.constant;

/**
 * 常量接口
 *
 * <AUTHOR>
 */
public interface Constants {
    String CHECKPOINT_DURATION="checkpoint.duration";
    String S3_EVENT_PATH="s3.event.path";

    String KAFKA_EVENT_SOURCE_TOPIC = "kafka.event.source.topic";
    String KAFKA_EVENT_SOURCE_BOOTSTRAP_SERVERS = "kafka.event.source.bootstrap.servers";
    String KAFKA_EVENT_GROUP = "kafka.event.group";
    String KAFKA_EVENT_SINK_TOPIC = "kafka.event.sink.topic";
    String KAFKA_EVENT_REPAIR_TOPIC = "kafka.event.repair.topic";
    String KAFKA_EVENT_SINK_BOOTSTRAP_SERVERS = "kafka.event.sink.bootstrap.servers";

    String KAFKA_OFFSET_KEY = "kafka_offset_key";
    String KAFKA_PARTITION_KEY = "kafka_partition_key";
    String KAFKA_GROUP_KEY = "kafka_group_key";

    /**
     * 项目配置相关的常量
     */
    String MY_ENV = "my.env";
    String JOB_NAME = "job.name";
    String CHECKPOINT_DIR="checkpoint.dir";

    String ROCKDBS_CHECKPOINT_DIR="state.checkpoint.dir";
    String WINDOW_SIZE="window.size";


    /**
     * clickhouse 链接信息
     */
    String CLICKHOUSE_BI_URL="clickhouse.bi.url";
    String CLICKHOUSE_BI_USER="clickhouse.bi.user";
    String CLICKHOUSE_BI_PASSWORD="clickhouse.bi.password";
    String CLICKHOUSE_BI_SIZE="clickhouse.bi.size";
    String CLICKHOUSE_TABLE_PERFORMANCE="clickhouse.table.performance";

    /**
     * clickhouse bj链接信息
     */
    String CLICKHOUSE_BJ_BI_URL="clickhouse.bj.bi.url";
    String CLICKHOUSE_BJ_BI_USER="clickhouse.bj.bi.user";
    String CLICKHOUSE_BJ_BI_PASSWORD="clickhouse.bj.bi.password";
    String CLICKHOUSE_BJ_BI_SIZE="clickhouse.bj.bi.size";
    String CLICKHOUSE_BJ_TABLE_BEHAVIOR="clickhouse.bj.table.behavior";


    /**
     * mysql 链接信息
     */
    String MYSQL_BI_URL="mysql.bi.url";
    String MYSQL_URL_CANARY = "******************************************";
    String MYSQL_USER_CANARY = "bi";
    String MYSQL_PASSWORD_CANARY = "VkizNiLd4Ec3rP0WsoI7";
    String MYSQL_BI_USER="mysql.bi.user";
    String MYSQL_BI_PASSWORD="mysql.bi.password";
    String MYSQL_BI_SIZE="mysql.bi.size";

    /**
     * pg 链接信息
     */
    String PG_MEMBER_URL="pg.member.url";
    String PG_MEMBER_USER="pg.member.user";
    String PG_MEMBER_PASSWORD="pg.member.password";
    String PG_MEMBER_SIZE="pg.member.size";


    /**
     * aws 信息
     */
    String AWS_REGION="aws.region";
    String AWS_ACCESS_KEY_ID="aws.access.key.id";
    String AWS_SECRET_ACCESS_KEY="aws.secret.access.key";

    /**
     * kinesis 信息
     */
    String STREAM_INITIAL_POSITION="stream.intital.position";
    String STREAM="stream";


    /**
     * hk kafka连接
     */
    String KAFKA_HK_HOST = "kafka.hk.host";
    String KAFKA_HK_GROUP = "kafka.hk.group";
    String KAFKA_HK_GROUP_BEHAVIOR = "kafka.hk.group.behavior";
    String KAFKA_HK_DEVICE_SINK_TOPIC = "kafka.hk.device.sink.topic";
    String KAFKA_HK_BEHAVIOR_SINK_TOPIC = "kafka.hk.behavior.sink.topic";
    String KAFKA_HK_PERFORMANCE_SINK_TOPIC = "kafka.hk.performance.sink.topic";
    String KAFKA_HK_PERFORMANCE_CK_SINK_TOPIC = "kafka.hk.performance.ck.sink.topic";

    /**
     * bj kafka链接
     */
    String KAFKA_BJ_HOST = "kafka.bj.host";
    String KAFKA_BJ_GROUP = "kafka.bj.group";
    String KAFKA_BJ_DEVICE_SINK_TOPIC = "kafka.bj.device.sink.topic";
    String KAFKA_BJ_BEHAVIOR_SINK_TOPIC = "kafka.bj.behavior.sink.topic";
    String KAFKA_BJ_PERFORMANCE_SINK_TOPIC = "kafka.bj.performance.sink.topic";


    String ENV = "env";

    String PHOENIX_SERVER = "phoenix.server";
    String HBASE_SCHEMA = "hbase.schema";
    String HBASE_TABLE = "hbase.table";

    /**
     * hive链接信息
     */

    String HIVE_URL = "hive.url";
    String HIVE_USER = "hive.user";
    String HIVE_PASSWORD = "hive.password";
    String HIVE_USER_PROFILE_TABLE = "hive.user.profile.table";
    String S3_CONFIG_PATH = "s3.config.path";
    String ALI_CONFIG_NAME = "ali.config.name";

    /**
     * 飞书报警配置
     */

    String feishu_URL = "feishu.url";
}
