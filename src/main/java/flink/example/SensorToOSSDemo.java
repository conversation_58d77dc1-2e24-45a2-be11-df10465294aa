package flink.example;

import com.alibaba.fastjson.JSONObject;
import flink.common.ConfigurationManager;
import flink.constant.Constants;
import flink.assigner.day.DayEventBucketAssigner;
import flink.function.EventFlatMap;
import flink.function.HbaseSensorSink;
import flink.function.MysqlSensorSink;
import flink.function.SensorSink;
import flink.model.EventModel;
import flink.util.BaseUtil;
import flink.util.KafkaUtils;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.serialization.SimpleStringEncoder;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.core.fs.Path;
import org.apache.flink.formats.parquet.avro.ParquetAvroWriters;
import org.apache.flink.runtime.state.StateBackend;
import org.apache.flink.runtime.state.filesystem.FsStateBackend;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.TimeCharacteristic;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.DataStreamSource;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.api.functions.sink.filesystem.StreamingFileSink;
import org.apache.flink.streaming.api.functions.windowing.ProcessAllWindowFunction;
import org.apache.flink.streaming.api.windowing.time.Time;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class SensorToOSSDemo {
    public static void main(String[] args) throws Exception, IOException {
        ConfigurationManager config = new ConfigurationManager(args);
        StreamExecutionEnvironment env = BaseUtil.getEnv(config);

//        final StreamExecutionEnvironment env = getEnv(config);



        KafkaSource<JSONObject> eventModelFlinkKafkaConsumer = KafkaUtils.getSourceWithOffset(config);

//        KafkaSource<JSONObject> sourceWithOffset = KafkaUtils.getSourceWithOffsetRepair(config);

//         神策修复数据
//        DataStreamSource<JSONObject> sensorSourceRepair = env.fromSource(sourceWithOffset, WatermarkStrategy.noWatermarks(), "SensorSourceRepair");

//         正常神策数据
        DataStreamSource<JSONObject> sensorSource = env.fromSource(eventModelFlinkKafkaConsumer, WatermarkStrategy.noWatermarks(), "SensorSource");

//         合并工作流
//        DataStream<JSONObject> sensorSourceResult = sensorSource.union(sensorSourceRepair);

//         解析神策数据
        SingleOutputStreamOperator<EventModel> eventFlatMapStream = sensorSource.flatMap(new EventFlatMap(config));

        OutputTag<EventModel> kafkaDataTag = new OutputTag<EventModel>("kafkaDataTag"){};

        SingleOutputStreamOperator<EventModel> S3DataResultStream = eventFlatMapStream.process(new ProcessFunction<EventModel, EventModel>() {
            @Override
            public void processElement(EventModel eventModel, ProcessFunction<EventModel, EventModel>.Context context, Collector<EventModel> collector) throws Exception {
                EventModel eventModelKafka = eventModel;
                context.output(kafkaDataTag, eventModelKafka);
                if(eventModel.getEvent() != null && eventModel.getType().contains("track")){
                    collector.collect(eventModel);
                }
            }
        });

        DataStream<EventModel> KafkaDataResultStream = S3DataResultStream.getSideOutput(kafkaDataTag);
//      发送数据到oss
        StreamingFileSink<EventModel> eventModelStreamingFileSink = eventSink(config);
        S3DataResultStream.addSink(eventModelStreamingFileSink);

//      发送数据到kafka
        KafkaSink kafkaSink = KafkaUtils.KafkaSinkExactlyOnce(config);
        KafkaDataResultStream.map(data -> data.toJson().toString()).sinkTo(kafkaSink);

//      发送数据到CK
//        Integer windowSize = config.getInteger(Constants.WINDOW_SIZE);
//        SingleOutputStreamOperator<List<EventModel>> CKDataStream = KafkaDataResultStream
//                .timeWindowAll(Time.milliseconds(windowSize))
//                .process(new ProcessAllWindowFunction<EventModel, List<EventModel>, TimeWindow>() {
//                    @Override
//                    public void process(Context context, Iterable<EventModel> iterable, Collector<List<EventModel>> collector) throws Exception {
//                        List<EventModel> EventModels = new ArrayList<>();
//                        for (EventModel EventModel : iterable) {
//                            EventModels.add(EventModel);
//                        }
//                        collector.collect(EventModels);
//                    }
//                });

        env.execute(SensorToOSSDemo.class.getSimpleName());
    }

    private static StreamingFileSink<EventModel> eventSink(ConfigurationManager config) {
        DayEventBucketAssigner<EventModel> myBucketAssigner = new DayEventBucketAssigner<>();
//        String path = config.getString(Constants.S3_EVENT_PATH);
        String path = config.getString(Constants.OUTPUT_OSS_DIR);

        StreamingFileSink<EventModel> build4 = StreamingFileSink
                .forBulkFormat(new Path(path),
                        ParquetAvroWriters.forReflectRecord(EventModel.class))
                .withBucketAssigner(myBucketAssigner)
                .build();

//        StreamingFileSink<String> build4 = StreamingFileSink.forRowFormat(
//                new Path(path),
//                new SimpleStringEncoder<String>("UTF-8")
//        ).build();
        return build4;
    }

    private static StreamExecutionEnvironment getEnv(ConfigurationManager prop) throws IOException {
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setStreamTimeCharacteristic(TimeCharacteristic.ProcessingTime);
        String myEnv = prop.getString(Constants.MY_ENV);
        CheckpointConfig checkpointConfig = env.getCheckpointConfig();
        checkpointConfig.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        checkpointConfig.setTolerableCheckpointFailureNumber(0);
        long cpTimeOut = prop.getInteger(Constants.CHECKPOINT_DURATION) - 10000L;
        if (cpTimeOut < 20000L) {
            cpTimeOut = 20000L;
        }
        checkpointConfig.setCheckpointTimeout(cpTimeOut);
        checkpointConfig.enableExternalizedCheckpoints(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
        StateBackend backend2 = new FsStateBackend(
                prop.getString(Constants.CHECKPOINT_DIR),
                true);
        env.setStateBackend(backend2);
        RestartStrategies.FailureRateRestartStrategyConfiguration failureRateRestartStrategyConfiguration =
                RestartStrategies.failureRateRestart(
                        10,
                        org.apache.flink.api.common.time.Time.minutes(10),
                        org.apache.flink.api.common.time.Time.seconds(15));
        env.setRestartStrategy(failureRateRestartStrategyConfiguration);
        env.enableCheckpointing(prop.getInteger(Constants.CHECKPOINT_DURATION));
        if ("prod".equalsIgnoreCase(myEnv) || "canary".equalsIgnoreCase(myEnv)) {
        } else if ("local".equalsIgnoreCase(prop.getString(Constants.MY_ENV))) {
            env.setParallelism(2);
        } else {
            System.exit(-1);
        }
        return env;
    }
}
