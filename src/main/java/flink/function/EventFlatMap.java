package flink.function;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.vdurmont.emoji.EmojiParser;
import flink.common.ConfigurationManager;
import flink.constant.Constants;
import flink.model.EventModel;
import flink.util.DecryptUtil;
import flink.util.IpUtils;
import org.apache.flink.api.common.functions.RichFlatMapFunction;
import org.apache.flink.util.Collector;
import org.lionsoul.ip2region.xdb.Searcher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
/**
 * 数据结构
 * {
 *     "header":[
 *         "User-Agent: Dalvik/2.1.0 (Linux; U; Android 10; HLK-AL00 Build/HONORHLK-AL00)",
 *         "Host: event-tracking.lbkrs.com"
 *         "client_ip" : **************
 *     ],
 *     "data":{
 *         "src":-1178349004,
 *         "gzip":1,
 *         "data_list":"H4sIAAAAAAAAAO1Xz4%2FbRBT%2BV5CzB5DWXo9%2FxYmEUES7ImJLK1rogUWjsWfsDLFnrJlxQigr9cwBeumpJyQqcesFiRv8NW3pf8Ebx%2BlmaVghbdsDTS7RvHk%2FvvfeN5%2BSr%2B452CiSzzGnztiNR0GIotBHh47hNXPGKEkTP07CKA2TERhXDRidLsI5dCjXhovcdMEO8kdxnKIQLipZcvGKlQgpVrVs9fomQwkpEIpdyB67EaKhmxGWuYhGI5IFfhikBKI4ZcJww5l2xvecg%2F64wkRQJTnFbdtli6IwioJh7ob5MHGjYVa4owxBShRnYYLStAiHkO08fhfGM4DOs64OfOOamZm0DqQ18k7f9EHn4UzW5XsDXjCluRRwkXihF1szaZotc%2BQFqedv3CkzhFdgLiuZkcqrpCgzxWnJPAjz%2Bt68G4SLSW74AgAPuo%2BFyBbQAsQeTJrmS86Wt3PFmIDUjZINUy9HJfVWfdTVriVltuwnJ5%2B6kxO%2Fs0l9sR3dpcNLTs0MGOCnPhgzBZhs4M3Pbn6%2B5TVjvJwBmCCM%2FMt7vljEOgpiKea8ePjns58fv%2Fe%2B0R9cMk3Lx%2B%2BkYFgWhWZQ0Y3STcVuiTsm2U%2BRsqwtu%2B6JaAuSm1YxZXv5YnL3%2BtTOrSKmkKrGPb%2FPcWat5oJpvbk5z275nOeyFQbnMyJEN9YqA7NUZU8rOGhDlMFLsoLz80e%2FPfvx8dMHT57%2B8KutSkq2mUGV4VwKA3vFiuWyBqQUF4zRjV9DFHiaDrch2Yc1wII7JVuzjh%2BfHp0eWdfTIxjJ6VEN1Ploy%2FNAsYIpaBwD%2B6sdc1%2Fygjtjo1oGB8HMUqr5pu270%2BPpdo41Yjt6W8betMr2D8i3FwCrFGypvZZ7hSJlDe15x1zQaytom%2BeW3ce9fb3incB6pvWT%2Bu8P5vurofl3AeAaF1xpg6ldq50YPEpcVK2e4YuymQQoTc4OL4gsgps0BM0b7hDZNN6L7F5k9yK7F9kOxNUUbK3Jl3X9FiTXVrQ4tzF%2FzFVesRMQtrchuEEajOLAR%2BGreht1v3TfUb3NQaZeu9RaAmgmtFSaEkOAJaRaGZ7rl3zRdO5NMm3HbW6vPa%2BB5%2BTWdDDodnDdKu8UGKsgdjDY7et9QxZkMEBhOtpWa0P0HHdveQGavVfrvVq%2FabXe%2BaSuJlguQmk4SoI4%2BKdixXGM3uG%2F4f9HxSpkVcklnssKs28bqeEh7mVrL1tvWLYs3dadRf2ph%2F%2FX709e3P%2Fp%2BS9%2FgNlSbGNvpAJOc%2BPazVm4RMyBUq9HAIdnX%2F8NePHKxIkUAAA%3D"
 *     },
 *     "project":"default",
 *     "offset":"",
 *     "groupid":"",
 *     "partition":""
 * }
 */
public class EventFlatMap extends RichFlatMapFunction<JSONObject, EventModel> {
    private static Logger log = LoggerFactory.getLogger(EventFlatMap.class);
    private Searcher searcher;
    ConfigurationManager config;
    public EventFlatMap(ConfigurationManager config) {
        this.config = config;
    }
    @Override
    public void flatMap(JSONObject value, Collector<EventModel> out) {
        JSONObject body = null;
        Long server_process_time = null;
        String source = null;
        String ip = null;
        try {
            body = value.getJSONObject("body");
            JSONObject header = value.getJSONObject("header");
            ip = header.getString("client_ip") == null ? "未知" : header.getString("client_ip");
            source = body.getString("source") == null ? "lb-ods-event-topic-r3p8" : value.getString("source");
            server_process_time = header.getLong("server_process_time")== null? 0: header.getLong("server_process_time");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        String sensorDataList = null;
        String sensorData = null;
        try {
            sensorDataList = body.getString("data_list");
            sensorData = body.getString("data");
            if(sensorDataList == null && sensorData == null){
                sensorDataList = body.getString("\"data_list");
                sensorDataList = sensorDataList.replace("\"", "");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        // kafka相关信息获取
        String kafka_offset = value.getString(Constants.KAFKA_OFFSET_KEY);
        String kafka_partition = value.getString(Constants.KAFKA_PARTITION_KEY);
        String kafka_group_id = value.getString(Constants.KAFKA_GROUP_KEY);
        String ip_region = getIpAddress(ip) == null? "未知" : getIpAddress(ip);
        String ip_region_replace = ip_region.replaceAll("0", "");
        String[] region = ip_region_replace.split("\\|");
        String country = null;
        String province = null;
        String city = null;
        try {
            country = "未知".equals(ip_region)? "未知" : region[0];
            province = region.length <= 3? "未知" : region[2];
            city = region.length <= 4? "未知" : region[3];
        } catch (Exception e) {
            log.error("ip_region : " + ip_region);
        }
        String project = value.getString("project");
        body.remove("project");
        /*
        H5 数据的server_api是sa.gif
        客户端 数据的server_api是sa
         */
        EventModel eventModel = new EventModel();
        if(sensorDataList == null){
            String resultString = "";
            JSONObject resultJson = null;
            // 埋点上报数据解析
            try {
                sensorData = EmojiParser.removeAllEmojis(sensorData);
                sensorData = DecryptUtil.urlDecode(sensorData);
                try {
                    if(!"1".equals(body.getString("gzip"))) {
                        resultString = DecryptUtil.base64Decode(sensorData);
                    } else {
                        resultString = DecryptUtil.unGzip(sensorData);
                    }
                } catch (Exception e) {

                }
                resultJson = JSONObject.parseObject(resultString);
                String distinct_id = resultJson.getString("distinct_id") == null || "".equals(resultJson.getString("distinct_id")) ? "unknown": resultJson.getString("distinct_id");
                String properties = resultJson.getString("properties") == null || "".equals(resultJson.getString("properties")) ? "unknown": resultJson.getString("properties");
                String anonymous_id = resultJson.getString("anonymous_id") == null || "".equals(resultJson.getString("anonymous_id")) ? "unknown": resultJson.getString("anonymous_id");
                String type = resultJson.getString("type") == null || "".equals(resultJson.getString("type")) ? "unknown": resultJson.getString("type");
                String event = resultJson.getString("event") == null || "".equals(resultJson.getString("event")) ? "unknown": resultJson.getString("event");
                Long time = resultJson.getLong("time") == null ? 0: resultJson.getLong("time");
                String track_id = resultJson.getString("_track_id") == null || "".equals(resultJson.getString("_track_id")) ? "unknown": resultJson.getString("_track_id");
                Long flush_time = resultJson.getLong("_flush_time") == null ? 0: resultJson.getLong("_flush_time");
                String error_message = "";
                if(event.contains("engine") && event.contains("message") && event.contains("[")){
                    error_message = event;
                    event = "error_message";
                }
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
                simpleDateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
                eventModel.setPt(simpleDateFormat.format(new Date(time)));
                eventModel.setDistinct_id(distinct_id);
//                eventModel.setProperties(properties);
                eventModel.setAnonymous_id(anonymous_id);
                eventModel.setType(type);
                eventModel.setEvent(event);
                eventModel.setTrack_id(track_id);
                eventModel.setFlush_time(flush_time);
                eventModel.setTime(time);
                eventModel.setKafka_group_id(kafka_group_id);
                eventModel.setKafka_offset(kafka_offset);
                eventModel.setKafka_partition(kafka_partition);
                eventModel.setProject(project);
                eventModel.setServer_process_time(server_process_time);
                eventModel.setKafka_offset_index(kafka_offset + "-0");
                eventModel.setSource(source);
                eventModel.setIp(ip);
                eventModel.setIp_region(ip_region);
                JSONObject jsonObject = JSONObject.parseObject(properties);
                jsonObject.put("$country", country);
                jsonObject.put("$province", province);
                jsonObject.put("$city", city);
                jsonObject.put("ip", ip);
                if(!"".equals(error_message)){
                    jsonObject.put("error_message", error_message);
                }
                // 新增字段page_name、 business_type、 platform_type、 account_channel
                String page_name = jsonObject.getString("page_name") == null || "".equals(jsonObject.getString("page_name"))? "unknown" : jsonObject.getString("page_name") ;
                String business_type = jsonObject.getString("business_type") == null || "".equals(jsonObject.getString("business_type"))?  "unknown": jsonObject.getString("business_type");
                String platform_type = jsonObject.getString("platform_type") == null || "".equals(jsonObject.getString("platform_type")) ?  "unknown" : jsonObject.getString("platform_type");
                String account_channel = jsonObject.getString("account_channel") == null || "".equals(jsonObject.getString("account_channel")) ? "unknown" : jsonObject.getString("account_channel");
                eventModel.setPage_name(page_name);
                eventModel.setBusiness_type(business_type);
                eventModel.setPlatform_type(platform_type);
                eventModel.setAccount_channel(account_channel);
                properties = jsonObject.toString();
                properties = Tranform(properties);
                eventModel.setProperties(properties);
                out.collect(eventModel);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                if(body != null && !body.isEmpty()){

                }
            }
        }
        else if(sensorData == null){
            // unGzip
            String resultString = null;
            sensorDataList = EmojiParser.removeAllEmojis(sensorDataList);
            sensorDataList = DecryptUtil.urlDecode(sensorDataList);
            try {
                if(body.getString("data_list")== null && sensorDataList != null){
                    resultString = DecryptUtil.base64Decode(sensorDataList);
                }else {
                    resultString = DecryptUtil.unGzip(sensorDataList);
                }
            } catch (Exception e) {
                try {
                    resultString = DecryptUtil.base64Decode(sensorDataList);
                } catch (Exception ex) {
                    log.error(e.getMessage(), e);

                }
            }
            JSONArray resultArray = null;
            try {
                resultArray = JSONArray.parseArray(resultString);
            } catch (Exception e) {

                log.error(e.getMessage(), e);
            }
            if(resultArray == null){
                resultArray = new JSONArray();
            }
            // jsonArray
            for (int i = 0; i < resultArray.size(); i++){
                JSONObject resultJson = (JSONObject) resultArray.get(i);
                try {
                    String distinct_id = resultJson.getString("distinct_id") == null || "".equals(resultJson.getString("distinct_id")) ? "unknown": resultJson.getString("distinct_id");
                    String properties = resultJson.getString("properties") == null || "".equals(resultJson.getString("properties")) ? "unknown": resultJson.getString("properties");
                    String anonymous_id = resultJson.getString("anonymous_id") == null || "".equals(resultJson.getString("anonymous_id")) ? "unknown": resultJson.getString("anonymous_id");
                    String type = resultJson.getString("type") == null || "".equals(resultJson.getString("type")) ? "unknown": resultJson.getString("type");
                    String event = resultJson.getString("event") == null || "".equals(resultJson.getString("event")) ? "unknown": resultJson.getString("event");
                    Long time = resultJson.getLong("time") == null ? 0: resultJson.getLong("time");
                    String track_id = resultJson.getString("_track_id") == null || "".equals(resultJson.getString("_track_id")) ? "unknown": resultJson.getString("_track_id");
                    Long flush_time = resultJson.getLong("_flush_time") == null ? 0: resultJson.getLong("_flush_time");
                    String error_message = "";
                    if(event.contains("engine") && event.contains("message") && event.contains("[")){
                        error_message = event;
                        event = "error_message";
                    }
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
                    simpleDateFormat.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
                    eventModel.setPt(simpleDateFormat.format(new Date(time)));
                    eventModel.setDistinct_id(distinct_id);
                    eventModel.setAnonymous_id(anonymous_id);
                    eventModel.setType(type);
                    eventModel.setEvent(event);
                    eventModel.setTrack_id(track_id);
                    eventModel.setFlush_time(flush_time);
                    eventModel.setTime(time);
                    eventModel.setKafka_group_id(kafka_group_id);
                    eventModel.setKafka_offset(kafka_offset);
                    eventModel.setKafka_partition(kafka_partition);
                    eventModel.setProject(project);
                    eventModel.setServer_process_time(server_process_time);
                    eventModel.setKafka_offset_index(kafka_offset + "-" + (i + 1));
                    eventModel.setSource(source);
                    eventModel.setIp(ip);
                    eventModel.setIp_region(ip_region);
                    JSONObject jsonObject = JSONObject.parseObject(properties);
                    jsonObject.put("$country", country);
                    jsonObject.put("$province", province);
                    jsonObject.put("$city", city);
                    jsonObject.put("ip", ip);
                    if(!"".equals(error_message)){
                        jsonObject.put("error_message", error_message);
                    }
                    // 新增字段page_name、 business_type、 platform_type、 account_channel
                    String page_name = jsonObject.getString("page_name") == null || "".equals(jsonObject.getString("page_name"))? "unknown" : jsonObject.getString("page_name") ;
                    String business_type = jsonObject.getString("business_type") == null || "".equals(jsonObject.getString("business_type"))?  "unknown": jsonObject.getString("business_type");
                    String platform_type = jsonObject.getString("platform_type") == null || "".equals(jsonObject.getString("platform_type")) ?  "unknown" : jsonObject.getString("platform_type");
                    String account_channel = jsonObject.getString("account_channel") == null || "".equals(jsonObject.getString("account_channel")) ? "unknown" : jsonObject.getString("account_channel");
                    eventModel.setPage_name(page_name);
                    eventModel.setBusiness_type(business_type);
                    eventModel.setPlatform_type(platform_type);
                    eventModel.setAccount_channel(account_channel);
                    properties = jsonObject.toString();
//
//                    String regexEmoticons = "[\\uD83C-\\uDBFF\\uDC00-\\uDFFF&&[^-]]+";
//                    Pattern emoticonPattern = Pattern.compile(regexEmoticons);
//                    Matcher emoticonMatcher = emoticonPattern.matcher(properties);
//
//                    // 删除匹配到的 emoji 表情
//                    properties = emoticonMatcher.replaceAll("");
                    properties = Tranform(properties);
                    eventModel.setProperties(properties);
                    out.collect(eventModel);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    if(body != null && !body.isEmpty()){

                    }
                }
            }
        }
    }
    public String getIpAddress(String ip){
        if ("127.0.0.1".equals(ip) || ip.startsWith("192.168")) {
            return "局域网 ip";
        }
        if (searcher == null) {
            try {
                String dbPath = IpUtils.createFtlFileByFtlArray();
                searcher = Searcher.newWithFileOnly(dbPath);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        String region = null;
        String errorMessage = null;
        try {
            region = searcher.search(ip);
        } catch (Exception e) {
            errorMessage = e.getMessage();
            if (errorMessage != null && errorMessage.length() > 256) {
                errorMessage = errorMessage.substring(0,256);
            }
            e.printStackTrace();
        }
        // 输出 region
        return region;
    }
    public String Tranform(String data){
        byte[] bytes = data.getBytes(StandardCharsets.UTF_8);
        String result = new String(bytes, StandardCharsets.UTF_8);
        return result;
    }
}