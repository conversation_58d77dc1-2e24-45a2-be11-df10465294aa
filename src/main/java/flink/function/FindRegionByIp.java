////
//// Source code recreated from a .class file by IntelliJ IDEA
//// (powered by FernFlower decompiler)
////
//
//package flink.function;
//
//import com.util.ip.IpRelation;
//import com.util.ip.IpTree;
//import java.io.BufferedReader;
//import java.io.InputStream;
//import java.io.InputStreamReader;
//import java.io.Serializable;
//import java.util.ArrayList;
//import java.util.Iterator;
//import java.util.List;
//
//public class FindRegionByIp implements Serializable {
//    private static final String UTF_8 = "UTF-8";
//    private static IpTree ipTree = IpTree.getInstance();
//    private static final String ipFile = "ip.csv";
//
//    public FindRegionByIp() {
//    }
//
//    private static void buildTrain() {
//        try {
//            List<IpRelation> ipRelationList = getIpRelation();
//            int count = 0;
//            Iterator var2 = ipRelationList.iterator();
//
//            while(var2.hasNext()) {
//                IpRelation ipRelation = (IpRelation)var2.next();
//                ipTree.train(ipRelation.getIpStart(), ipRelation.getIpEnd(), ipRelation.getProvince());
//                if (count > 10) {
//                    break;
//                }
//            }
//        } catch (Exception var4) {
//            var4.printStackTrace();
//        }
//
//    }
//
//    public String findRegionByIp(String ip) {
//        return ipTree.findIp(ip);
//    }
//
//    public static List<IpRelation> getIpRelation() throws Exception {
//        BufferedReader ipRelationReader = readFile(FindRegionByIp.class.getClassLoader().getResourceAsStream("ip.csv"));
//        List<IpRelation> list = new ArrayList();
//
//        String line;
//        while((line = ipRelationReader.readLine()) != null) {
//            String[] split = line.split(",");
//            String ipStart = split[0];
//            String ipEnd = split[1];
//            StringBuffer sb = new StringBuffer();
//
//            for(int i = 2; i < split.length; ++i) {
//                String temp = "0".equals(split[i]) ? "" : split[i];
//                sb.append(i != 2 ? "-" + temp : temp);
//            }
//
//            String province = sb.toString();
//            IpRelation ipRelation = new IpRelation();
//            ipRelation.setIpStart(ipStart);
//            ipRelation.setIpEnd(ipEnd);
//            ipRelation.setProvince(province);
//            list.add(ipRelation);
//        }
//
//        return list;
//    }
//
//    public static BufferedReader readFile(InputStream is) throws Exception {
//        return new BufferedReader(new InputStreamReader(is, "UTF-8"));
//    }
//
//    static {
//        buildTrain();
//    }
//}
