package flink.function;

import flink.common.ConfigurationManager;
import flink.constant.Constants;
import flink.model.EventModel;
import flink.util.PhoenixUtils;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;

public class HbaseSensorSink extends RichSinkFunction<EventModel> {

    static Logger log = LoggerFactory.getLogger(HbaseSensorSink.class);
    ConfigurationManager config;

    public HbaseSensorSink(ConfigurationManager config) {
        this.config = config;
    }

    @Override
    public void invoke(EventModel value, Context context) throws Exception {

        Connection connHbaseWatch = PhoenixUtils.getConnection(config.getString(Constants.PHOENIX_SERVER));

        PreparedStatement preparedStatement = connHbaseWatch.prepareStatement("select * from bi.flink_test_demo limit 1");

        boolean executeRead = preparedStatement.execute();

        log.error("Mysql 读操作成功： " + executeRead);

        boolean executeWrite = preparedStatement.execute(String.format("upsert into bi.flink_test_demo (id, update_date) value (1, %s)", System.currentTimeMillis()));

        log.error("Mysql 写操作成功：" + executeWrite);

    }
}
