package flink.function;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.pool.DruidPooledConnection;
import flink.common.ConfigurationManager;
import flink.model.EventModel;
import flink.util.DruidUtils;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.PreparedStatement;

public class MysqlSensorSink extends RichSinkFunction<EventModel> {

    DruidDataSource mysql = null;
    static Logger log = LoggerFactory.getLogger(MysqlSensorSink.class);
    ConfigurationManager config;

    public MysqlSensorSink(ConfigurationManager config) {
        this.config = config;
    }

    @Override
    public void invoke(EventModel value, Context context) throws Exception {
        mysql = DruidUtils.init(config, "MYSQL");
        DruidPooledConnection connection = mysql.getConnection();

        PreparedStatement preparedStatement = connection.prepareStatement("select * from bi.flink_test_demo limit 1");

        boolean executeRead = preparedStatement.execute();

        log.error("Mysql 读操作成功： " + executeRead);

        boolean executeWrite = preparedStatement.execute(String.format("insert into bi.flink_test_demo (id, update_date) value (1, %s)", System.currentTimeMillis()));

        log.error("Mysql 写操作成功：" + executeWrite);

    }
}
