package flink.function;

import flink.common.ConfigurationManager;
import flink.constant.Constants;
import flink.jdbcHelper.JDBCHelperClickhouse;
import flink.model.BaseAlert;
import flink.model.EventModel;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.sink.RichSinkFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;


public class SensorSink extends RichSinkFunction<List<EventModel>> {
    private static Logger logger = LoggerFactory.getLogger(SensorSink.class);

    ConfigurationManager prop;
    private JDBCHelperClickhouse jDBCHelperClickhouse;
    String sql = "";

    public SensorSink(ConfigurationManager prop) {
        super();
        this.prop = prop;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        String url = prop.getString(Constants.CLICKHOUSE_BI_URL);
        String user = prop.getString(Constants.CLICKHOUSE_BI_USER);
        String password = prop.getString(Constants.CLICKHOUSE_BI_PASSWORD);
        Integer size = prop.getInteger(Constants.CLICKHOUSE_BI_SIZE);
        String sensorTable = prop.getString(Constants.CLICKHOUSE_TABLE_SENSOR);
        sql = "insert into table " + sensorTable +
                " (distinct_id, project, properties, kafka_offset, kafka_offset_index, kafka_partition, time) " +
                " values (?,?,?,?,?,?,?) ";
        jDBCHelperClickhouse = JDBCHelperClickhouse.getInstance(url, user, password, size);
        super.open(parameters);
    }

    @Override
    public void invoke(List<EventModel> EventModelList, Context context) throws Exception {
        List<Object[]> objectList = new ArrayList<>();
        for (int i = 0; i < EventModelList.size(); i++) {
            EventModel EventModel = EventModelList.get(i);
            Object[] objects = new Object[7];
            objects[0] = EventModel.getDistinct_id();
            objects[1] = EventModel.getProject();
            String properties = EventModel.getProperties();
            objects[2] = properties;
            objects[3] = EventModel.getKafka_offset();
            objects[4] = EventModel.getKafka_offset_index();
            objects[5] = EventModel.getKafka_partition();
            objects[6] = EventModel.getTime();

            objectList.add(objects);
        }
        try {
            jDBCHelperClickhouse.executeBatch(sql, objectList);
        } catch (Exception e) {
            BaseAlert.sensorAlert(prop.getString(Constants.feishu_URL), "属性值不符合规范", objectList.toString());
        }

    }

    @Override
    public void close() throws Exception {
        super.close();

    }
}
