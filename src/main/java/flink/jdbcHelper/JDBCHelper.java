package flink.jdbcHelper;


import com.alibaba.druid.pool.DruidDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

public class JDBCHelper {

    private static Logger log = LoggerFactory.getLogger(JDBCHelper.class);
    public DruidDataSource dataSource = new DruidDataSource();

    public synchronized Connection getConnection() throws Exception{
        Connection connection = null;
        try {
            connection = dataSource.getConnection();
        } catch (SQLException e) {
            log.error("", e);
            throw e;
        } finally {
        }

        return connection;
    }

    public synchronized DruidDataSource getDatasource() {
        return dataSource;
    }


    public void returnConnection(Connection conn)  throws Exception{
        try {
            conn.close();
        } catch (SQLException e) {
            log.error("", e);
            throw e;
        } finally {
        }
    }


    public int executeUpdate(String sql, Object[] params) throws Exception {
        int rtn = 0;
        Connection conn = null;
        PreparedStatement pstmt = null;

        try {
            conn = getConnection();
            conn.setAutoCommit(false);

            pstmt = conn.prepareStatement(sql);

            if (params != null && params.length > 0) {
                for (int i = 0; i < params.length; i++) {
                    pstmt.setObject(i + 1, params[i]);
                }
            }

            rtn = pstmt.executeUpdate();

            conn.commit();
        } catch (Exception e) {
            log.error("", e);
            throw e;
        } finally {
            if (pstmt != null) {
                try {
                    pstmt.close();
                } catch (SQLException e) {
                    log.error("", e);
                    throw e;
                }
            }
            if (conn != null) {
                returnConnection(conn);
            }
        }

        return rtn;
    }


    public void executeQuery(String sql, Object[] params,
                             QueryCallback callback)  throws Exception{
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;

        try {
            conn = getConnection();
            pstmt = conn.prepareStatement(sql);

            if (params != null && params.length > 0) {
                for (int i = 0; i < params.length; i++) {
                    pstmt.setObject(i + 1, params[i]);
                }
            }

            rs = pstmt.executeQuery();

            callback.process(rs);
        } catch (Exception e) {
            log.error("", e);
            throw e;
        } finally {
            if (rs != null) {
                try {
                    rs.close();
                } catch (SQLException e) {
                    log.error("", e);
                    throw e;
                }
            }
            if (pstmt != null) {
                try {
                    pstmt.close();
                } catch (SQLException e) {
                    log.error("", e);
                    throw e;
                }
            }
            if (conn != null) {
                returnConnection(conn);
            }
        }
    }

    public Object executeQuery2(String sql, Object[] params,
                                QueryCallback2 callback)  throws Exception{
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        Object object = null;
        try {
            conn = getConnection();
            pstmt = conn.prepareStatement(sql);

            if (params != null && params.length > 0) {
                for (int i = 0; i < params.length; i++) {
                    pstmt.setObject(i + 1, params[i]);
                }
            }

            rs = pstmt.executeQuery();

            object = callback.process(rs);
        } catch (Exception e) {
            log.error("", e);
            throw e;
        } finally {
            if (rs != null) {
                try {
                    rs.close();
                } catch (SQLException e) {
                    log.error("", e);
                    throw e;
                }
            }
            if (pstmt != null) {
                try {
                    pstmt.close();
                } catch (SQLException e) {
                    log.error("", e);
                    throw e;
                }
            }
            if (conn != null) {
                returnConnection(conn);
            }
        }
        return object;
    }

    public ResultSet executeQuery3(String sql, Object[] params)  throws Exception{
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        try {
            conn = getConnection();
            pstmt = conn.prepareStatement(sql);

            if (params != null && params.length > 0) {
                for (int i = 0; i < params.length; i++) {
                    pstmt.setObject(i + 1, params[i]);
                }
            }

            rs = pstmt.executeQuery();
        } catch (Exception e) {
            log.error("", e);
            throw e;
        } finally {
            if (rs != null) {
                try {
                    rs.close();
                } catch (SQLException e) {
                    log.error("", e);
                    throw e;
                }
            }
            if (pstmt != null) {
                try {
                    pstmt.close();
                } catch (SQLException e) {
                    log.error("", e);
                    throw e;
                }
            }
            if (conn != null) {
                returnConnection(conn);
            }
        }
        return rs;
    }


    public int[] executeBatch(String sql, List<Object[]> paramsList)  throws Exception{
        int[] rtn = null;
        Connection conn = null;
        PreparedStatement pstmt = null;

        try {
            conn = getConnection();


            conn.setAutoCommit(false);

            pstmt = conn.prepareStatement(sql);


            if (paramsList != null && paramsList.size() > 0) {
                for (Object[] params : paramsList) {
                    for (int i = 0; i < params.length; i++) {
                        pstmt.setObject(i + 1, params[i]);
                    }
                    pstmt.addBatch();
                }
            }

            rtn = pstmt.executeBatch();


            conn.commit();
        } catch (Exception e) {
            log.error("", e);
            throw e;
        } finally {
            if (pstmt != null) {
                try {
                    pstmt.close();
                } catch (SQLException e) {
                    log.error("", e);
                    throw e;
                }
            }
            if (conn != null) {
                returnConnection(conn);
            }
        }

        return rtn;
    }


    public static interface QueryCallback {


        void process(ResultSet rs) throws Exception;

    }

    public static interface QueryCallback2 {

        Object process(ResultSet rs) throws Exception;


    }

}
