package flink.jdbcHelper;


public class <PERSON><PERSON><PERSON>HelperBi extends <PERSON><PERSON><PERSON><PERSON>el<PERSON> {

	private static volatile JDBCHelperBi instance = null;


	public static JDBCHelperBi getInstance(String url, String user, String password, int datasourceSize) {
		if(instance == null) {
			synchronized(JDBCHelperBi.class) {
				if(instance == null) {
					instance = new JDBCHelperBi( url,user, password,datasourceSize);
				}
			}
		}
		return instance;
	}
	private JDBCHelperBi(String url, String user, String password, int datasourceSize) {
		dataSource.setDriverClassName("com.mysql.jdbc.Driver");
		dataSource.setUsername(user);
		dataSource.setPassword(password);
		dataSource.setUrl(url);
		dataSource.setInitialSize(datasourceSize);
		dataSource.setMinIdle(datasourceSize);
		dataSource.setMaxActive(datasourceSize+10);
		dataSource.setValidationQuery("select 1");
		dataSource.setTestOnBorrow(true);
		dataSource.setTestOnReturn(false);
		dataSource.setTestWhileIdle(true);
		dataSource.setMaxWait(60*1000);
		dataSource.setTimeBetweenEvictionRunsMillis(60*1000);
		dataSource.setMinEvictableIdleTimeMillis(300*1000);
	}



}
