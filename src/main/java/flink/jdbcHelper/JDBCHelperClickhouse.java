package flink.jdbcHelper;



public class JDBCHelperClickhouse extends <PERSON><PERSON><PERSON>Hel<PERSON> {

	private static volatile JDBCHelperClickhouse instance = null;


	public static JDBCHelperClickhouse getInstance(String url, String user, String password, int datasourceSize) {
		if(instance == null) {
			synchronized(JDBCHelperClickhouse.class) {
				if(instance == null) {
					instance = new JDBCHelperClickhouse( url,user, password,datasourceSize);
				}
			}
		}
		return instance;
	}

	private JDBCHelperClickhouse(String url, String user, String password, int datasourceSize) {
		dataSource.setDriverClassName("ru.yandex.clickhouse.ClickHouseDriver");
		dataSource.setUsername(user);
		dataSource.setPassword(password);
		dataSource.setUrl(url);
		dataSource.setInitialSize(datasourceSize);
		dataSource.setMinIdle(datasourceSize);
		dataSource.setMaxActive(datasourceSize+10);
		dataSource.setValidationQuery("select 1");
		dataSource.setTestOnBorrow(true);
		dataSource.setTestOnReturn(false);
		dataSource.setTestWhileIdle(true);
		dataSource.setMaxWait(60*1000);
		dataSource.setTimeBetweenEvictionRunsMillis(60*1000);
		dataSource.setMinEvictableIdleTimeMillis(300*1000);
	}



}
