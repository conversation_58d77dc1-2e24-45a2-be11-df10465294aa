package flink.jdbcHelper;



public class J<PERSON><PERSON><PERSON>elperClickhouseBJ extends JDBCHelper {

	private static volatile JDBCHelperClickhouseBJ instance = null;


	public static JDBCHelperClickhouseBJ getInstance(String url, String user, String password, int datasourceSize) {
		if(instance == null) {
			synchronized(JDBCHelperClickhouseBJ.class) {
				if(instance == null) {
					instance = new JDBCHelperClickhouseBJ( url,user, password,datasourceSize);
				}
			}
		}
		return instance;
	}

	private JDBCHelperClickhouseBJ(String url, String user, String password, int datasourceSize) {
		dataSource.setDriverClassName("ru.yandex.clickhouse.ClickHouseDriver");
		dataSource.setUsername(user);
		dataSource.setPassword(password);
		dataSource.setUrl(url);
		dataSource.setInitialSize(datasourceSize);
		dataSource.setMinIdle(datasourceSize);
		dataSource.setMaxActive(datasourceSize+10);
		dataSource.setValidationQuery("select 1");
		dataSource.setTestOnBorrow(true);
		dataSource.setTestOnReturn(false);
		dataSource.setTestWhileIdle(true);
		dataSource.setMaxWait(60*1000);
		dataSource.setTimeBetweenEvictionRunsMillis(60*1000);
		dataSource.setMinEvictableIdleTimeMillis(300*1000);
	}



}
