package flink.jdbcHelper;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class JD<PERSON>HelperMember extends JDBCHelper {
	private static Logger log = LoggerFactory.getLogger(JDBCHelperMember.class);

	private static volatile JDBCHelperMember instance = null;


	public static JDBCHelperMember getInstance(String url, String user, String password, int datasourceSize) {
		if(instance == null) {
			synchronized(JDBCHelperMember.class) {
				if(instance == null) {
					instance = new JDBCHelperMember( url,user, password,datasourceSize);
				}
			}
		}
		return instance;
	}
	private JDBCHelperMember(String url, String user, String password, int datasourceSize) {
		log.info("init JDBCHelperMember......");
		log.info("url:{}",url);
		log.info("user:{}",user);
		log.info("password:{}",password);
		log.info("datasourceSize:{}",datasourceSize);
		dataSource.setDriverClassName("org.postgresql.Driver");
		dataSource.setUsername(user);
		dataSource.setPassword(password);
		dataSource.setUrl(url);
		dataSource.setInitialSize(datasourceSize);
		dataSource.setMinIdle(datasourceSize);
		dataSource.setMaxActive(datasourceSize+10);
		dataSource.setValidationQuery("select 1");
		dataSource.setTestOnBorrow(true);
		dataSource.setTestOnReturn(false);
		dataSource.setTestWhileIdle(true);
		dataSource.setMaxWait(60*1000);
		dataSource.setTimeBetweenEvictionRunsMillis(60*1000);
		dataSource.setMinEvictableIdleTimeMillis(300*1000);
	}



}
