package model;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.Serializable;
import java.util.ArrayList;
/**
 * <AUTHOR>
 * @date 2022/3/16 4:03 下午
 */
public class Alert extends model.BaseAlert implements Serializable {
    private final String url;
    private final String prefix;
    //消息通知可用环境
    private static final Logger log = LoggerFactory.getLogger(Alert.class);
    public Alert(String url) {
        this.url = url;
        this.prefix = "";
    }
    public Alert(String url, String prefix) {
        this.url = url;
        this.prefix = "[" + prefix + "]-";
    }
    /**
     * 提取信息
     *
     * @param msg 信息
     * @return String
     */
    private String substringMsg(String msg) {
        if (StringUtils.isEmpty(msg)) {
            return " msg is empty";
        }
        int limitLength = 500;
        int maxLength = msg.length() > limitLength ? limitLength : msg.length() - 1;
        return msg.substring(0, maxLength);
    }
    /**
     * 圈人系统报警（重写）
     *
     * @param msg      msg
     * @param errorMsg 异常信息
     */
    @Override
    public void alert(String msg, String errorMsg) {
        ArrayList<String> msgList = new ArrayList<>();
        msgList.add(msg);
        msgList.add(substringMsg(errorMsg));
        baseAlert(url, prefix + "Flink任务告警", msgList);
    }
}

