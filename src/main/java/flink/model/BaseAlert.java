package flink.model;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import flink.util.HttpclientUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
/**
 * <AUTHOR>
 * @date 2022/9/16 4:12 下午
 */
public abstract class BaseAlert implements Serializable {
    /**
     * 需要重写的真正报警方法
     *
     * @param msg      消息简要
     * @param errorMsg 消息体
     */
    public abstract void alert(String msg, String errorMsg);
    /**
     * 向飞书报警机器人发送消息(基础方法)
     *
     * @param url     飞书地址（每个报警群具有唯一标识）
     * @param title   报警标题
     * @param msgList 消息列表（飞书展示：会在同一条报警中，以换行符隔开）
     * @return 请求结果集
     */
    public static String baseAlert(String url, String title, List<String> msgList) {
        //按照富文本格式将内容封装
        ArrayList<ArrayList<JSONObject>> jsonObjects = new ArrayList<>();
        for (String entity : msgList) {
            ArrayList<JSONObject> temList = new ArrayList<>();
            JSONObject tem = new JSONObject();
            tem.put("tag", "text");
            tem.put("text", "\n" + entity + "\n");
            temList.add(tem);
            jsonObjects.add(temList);
        }
        JSONArray array = JSONArray.parseArray(JSONObject.toJSONString(jsonObjects));
        JSONObject zhCnValue = new JSONObject();
        zhCnValue.put("title", title);
        zhCnValue.put("content", array);
        JSONObject postValue = new JSONObject();
        postValue.put("zh_cn", zhCnValue);
        JSONObject contentValue = new JSONObject();
        contentValue.put("post", postValue);
        JSONObject result = new JSONObject();
        result.put("msg_type", "post");
        result.put("content", contentValue);
        //--封装结束--
        //发送
        return HttpclientUtils.doPost(url, result.toJSONString());
    }

    public static String sensorAlert(String url, String title, String msg) {
        //按照富文本格式将内容封装
        ArrayList<ArrayList<JSONObject>> jsonObjects = new ArrayList<>();
        ArrayList<JSONObject> temList = new ArrayList<>();
        JSONObject tem = new JSONObject();
        tem.put("tag", "text");
        tem.put("text", "\n" + msg + "\n");
        temList.add(tem);
        jsonObjects.add(temList);
        JSONArray array = JSONArray.parseArray(JSONObject.toJSONString(jsonObjects));
        JSONObject zhCnValue = new JSONObject();
        zhCnValue.put("title", title);
        zhCnValue.put("content", array);
        JSONObject postValue = new JSONObject();
        postValue.put("zh_cn", zhCnValue);
        JSONObject contentValue = new JSONObject();
        contentValue.put("post", postValue);
        JSONObject result = new JSONObject();
        result.put("msg_type", "post");
        result.put("content", contentValue);
        //--封装结束--
        //发送
        return HttpclientUtils.doPost(url, result.toJSONString());
    }
}