package flink.model;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
@Data
//@ToString
public class EventModel {
    private String distinct_id;
    private Long time;
    private String type;
    private String event;
    private String project;
    private String properties;
    private String kafka_offset;
    private String kafka_offset_index;
    private String kafka_group_id;
    private String kafka_partition;
    private Long flush_time;
    private String track_id;
    private String anonymous_id;
    private Long server_process_time;
    private String source;
    private String ip;
    private String ip_region;
    private String pt;
    private String page_name;
    private String business_type;
    private String platform_type;
    private String account_channel;
    public JSONObject toJson(){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("distinct_id", distinct_id);
        jsonObject.put("time", time);
        jsonObject.put("type", type);
        if(!StringUtils.isEmpty(event)) {
            jsonObject.put("event", event);
        }
        jsonObject.put("project", project);
        jsonObject.put("properties", JSONObject.parseObject(properties));
        jsonObject.put("kafka_offset", kafka_offset);
        jsonObject.put("kafka_group_id", kafka_group_id);
        jsonObject.put("kafka_partition", kafka_partition);
        jsonObject.put("flush_time", flush_time);
        jsonObject.put("track_id", track_id);
        jsonObject.put("anonymous_id", anonymous_id);
        jsonObject.put("kafka_offset_index", kafka_offset_index);
        jsonObject.put("server_process_time", server_process_time);
        jsonObject.put("source", source);
        jsonObject.put("ip", ip);
        jsonObject.put("ip_region", ip_region);
        jsonObject.put("page_name", page_name);
        jsonObject.put("business_type", business_type);
        jsonObject.put("platform_type", platform_type);
        jsonObject.put("account_channel", account_channel);
        jsonObject.put("pt", pt);
        return jsonObject;
    }
}