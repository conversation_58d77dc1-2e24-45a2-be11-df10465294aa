package flink.util;

import flink.common.ConfigurationManager;
import flink.constant.Constants;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.TimeCharacteristic;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import java.io.IOException;

public class BaseUtil {
    public static StreamExecutionEnvironment getEnv(ConfigurationManager prop) throws IOException {
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setStreamTimeCharacteristic(TimeCharacteristic.ProcessingTime);
        String myEnv = prop.getString(Constants.MY_ENV);
        CheckpointConfig checkpointConfig = env.getCheckpointConfig();
        checkpointConfig.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
        checkpointConfig.setTolerableCheckpointFailureNumber(12);
        checkpointConfig.setCheckpointTimeout(30000L);
        checkpointConfig.enableExternalizedCheckpoints(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
//        StateBackend backend2 = new RocksDBStateBackend(
//                prop.getString(Constants.CHECKPOINT_DIR),
//                true);
//
//        env.setStateBackend(backend2);
        RestartStrategies.FailureRateRestartStrategyConfiguration failureRateRestartStrategyConfiguration =
                RestartStrategies.failureRateRestart(
                        10,
                        org.apache.flink.api.common.time.Time.minutes(10),
                        org.apache.flink.api.common.time.Time.seconds(15));
        env.setRestartStrategy(failureRateRestartStrategyConfiguration);
        if ("prod".equalsIgnoreCase(myEnv) || "staging".equalsIgnoreCase(myEnv)) {
            env.enableCheckpointing(60000L);
        } else if ("local".equalsIgnoreCase(prop.getString(Constants.MY_ENV))) {
            env.enableCheckpointing(5000L);
            env.setParallelism(2);
        } else {
            System.exit(-1);
        }
        return env;
    }
}


