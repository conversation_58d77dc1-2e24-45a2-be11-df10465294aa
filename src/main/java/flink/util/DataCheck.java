package flink.util;

public class DataCheck {

    public static String checkstr(String s) {
        return "".equalsIgnoreCase(s) ? "" : s;
    }

    public static Integer checInt(String s) {
        Integer integer = null;
        try {
            integer = "".equalsIgnoreCase(s) ? null : Integer.valueOf(s);
        } catch (Exception e) {
        }
        return integer;
    }
    public static Integer checkInt(String s) {
        try {
            Integer integer = Integer.valueOf(s);
            return integer;
        } catch (NumberFormatException e) {
            return -1;
        }
    }

    public static Float checkFloat(String s) {
        try {
            Float aFloat = Float.valueOf(s);
            return aFloat;
        } catch (NumberFormatException e) {
            return -1F;
        }
    }

    public static Double checkDouble(String s) {
        try {
            Double aFloat = Double.valueOf(s);
            return aFloat;
        } catch (NumberFormatException e) {
            return -1.0;
        }
    }

}
