package flink.util;

import org.apache.commons.lang3.StringUtils;
import org.eclipse.jetty.util.UrlEncoded;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.zip.GZIPInputStream;

public class DecryptUtil {
    public static String base64Encode(String value){
        Base64.Encoder encoder = Base64.getEncoder();
        byte[] decode = encoder.encode(value.getBytes());
        String result = new String(decode);
        return result;
    }

    public static String base64Decode(String value){
        Base64.Decoder decoder = Base64.getDecoder();
        byte[] decode = decoder.decode(value);
        String result = new String(decode);
        return result;
    }

    public static String urlDecode(String value){
        String result = UrlEncoded.decodeString(value);
        return result;
    }

    public static String urlEncode(String value){
        String result = UrlEncoded.encodeString(value);
        return result;
    }

    public static String unGzip(String string) throws IOException {
        String result = "";
        if (StringUtils.isBlank(string)) {
            return result;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream in;
        GZIPInputStream ungzip;
        byte[] bytes = Base64.getDecoder().decode(string);
        in = new ByteArrayInputStream(bytes);
        ungzip = new GZIPInputStream(in);
        byte[] buffer = new byte[10240];
        int len = 0;
        while ((len = ungzip.read(buffer)) != -1) {
            out.write(buffer, 0, len);
        }
        ungzip.close();
        out.close();
        in.close();
        result = out.toString("UTF-8");
        return result;
    }
}
