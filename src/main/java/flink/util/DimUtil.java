package flink.util;

import com.alibaba.fastjson.JSONObject;

/**
 * @version 1.0
 * @<PERSON> <PERSON><PERSON><PERSON><PERSON>
 * @create 2021/9/25 22:04
 * @Desc 基于PhoenixUtils封装
 */
public class DimUtil {
    public static JSONObject StringToJson(String type, String counter_id, String members, String columnName){
        JSONObject resultJson = new JSONObject();

//        String members = member_Ids.toString().replace("[", "").replace("]", "").replace(" ", "");
//        members = "".equals(members) ? "null" : members;


        resultJson.put("type", type);
        resultJson.put("member_id", members);
        resultJson.put("counter_id", counter_id);
        resultJson.put("column_name", columnName);
        return resultJson;
    }

}