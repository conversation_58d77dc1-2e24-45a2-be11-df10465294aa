package flink.util;


import com.alibaba.druid.pool.DruidDataSource;
import flink.common.ConfigurationManager;
import flink.constant.Constants;
import flink.constant.DBconfig;

/**
 * @version 1.0
 * @<PERSON> <PERSON><PERSON><PERSON><PERSON>
 * @create 2021/9/24 19:47
 */
public class DruidUtils {
    private static DruidDataSource dataSource;
    private static String URL;
    private static String USER;
    private static String PASSWORD;

    public static DruidDataSource init(ConfigurationManager config, String type) throws Exception {

        String MYSQL_URL = null;
        String MYSQL_USER = null;
        String MYSQL_PASSWORD = null;

        String PG_URL = null;
        String PG_USER = null;
        String PG_PASSWORD = null;

        dataSource = new DruidDataSource();
        switch (type) {
            case "MYSQL":
                dataSource.setDriverClassName(DBconfig.MYSQL_DRIVER);
                MYSQL_URL = config.getString(Constants.MYSQL_URL_CANARY);
                MYSQL_USER = config.getString(Constants.MYSQL_USER_CANARY);
                MYSQL_PASSWORD = config.getString(Constants.MYSQL_PASSWORD_CANARY);
                break;
            case "PSQL":
                dataSource.setDriverClassName(DBconfig.PSQL_DRIVER);
                break;
        }
        URL = MYSQL_URL == null? PG_URL : MYSQL_URL;
        USER = MYSQL_USER == null? PG_USER : MYSQL_USER;
        PASSWORD = MYSQL_PASSWORD == null? PG_PASSWORD : MYSQL_PASSWORD;

        dataSource.setUrl(URL);
        dataSource.setUsername(MYSQL_USER);
        dataSource.setPassword(MYSQL_PASSWORD);

        //设置初始化连接数，最大连接数，最小闲置数
        dataSource.setInitialSize(15);
        dataSource.setMaxActive(15);
        dataSource.setMinIdle(15);
        dataSource.setMaxWait(1000 * 30);
        dataSource.setMaxEvictableIdleTimeMillis(1000 * 60 * 60 * 10);
        dataSource.setMinEvictableIdleTimeMillis(1000 * 60 * 60 * 9);
        dataSource.setTimeBetweenEvictionRunsMillis(10 * 1000);
        dataSource.setValidationQuery("select 1");
        dataSource.setTestWhileIdle(true);
        dataSource.setTestOnBorrow(false);
        dataSource.setTestOnReturn(false);
        dataSource.setPoolPreparedStatements(false);

        return dataSource;
    }
}
