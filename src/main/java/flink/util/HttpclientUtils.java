package utils;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
/**
 * <AUTHOR>
 * @date 2022/1/19 3:05 下午
 */
public class HttpclientUtils {
    private static final class DefaultRequestConfigHolder {
        /**
         * 连接超时时间
         */
        private static final int CONNECT_TIME_OUT = 3000;
        /**
         * 连接请求超时时间
         */
        private static final int CONNECT_REQUEST_TIME_OUT = 3000;
        /**
         * socket读写超时时间
         */
        private static final int SOCKET_TIME_OUT = 5000;
        /**
         * 默认的请求配置
         */
        static final RequestConfig defaultRequestConfig = RequestConfig.custom()
                .setConnectTimeout(CONNECT_TIME_OUT)
                .setConnectionRequestTimeout(CONNECT_REQUEST_TIME_OUT)
                .setSocketTimeout(SOCKET_TIME_OUT)
                // 设置是否允许重定向 默认为true
                .setCircularRedirectsAllowed(true)
                .build();
    }
    /**
     * get请求
     *
     * @param url url
     * @return String
     */
    public static String doGet(String url) {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpGet httpGet = new HttpGet(url);
        httpGet.setConfig(DefaultRequestConfigHolder.defaultRequestConfig);
        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(httpGet);
            return EntityUtils.toString(response.getEntity());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
    /**
     * get请求
     *
     * @param url    url
     * @param params 参数
     * @return String
     */
    public static String doGet(String url, Map<String, Object> params) {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        if (null != params && !params.isEmpty()) {
            StringBuilder stringBuilder = new StringBuilder(url);
            stringBuilder.append("?");
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                stringBuilder.append(entry.getKey()).append("=")
                        .append(entry.getValue()).append("&");
            }
            url = stringBuilder.toString();
        }
        HttpGet httpGet = new HttpGet(url);
        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(httpGet);
            return EntityUtils.toString(response.getEntity());
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                if (httpClient != null) {
                    httpClient.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
    /**
     * post请求
     *
     * @param url     url
     * @param jsonStr json参数
     * @return String
     */
    public static String doPost(String url, String jsonStr) {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        CloseableHttpResponse response = null;
        StringEntity stringEntity = new StringEntity(jsonStr, StandardCharsets.UTF_8);
        HttpPost httpPost = new HttpPost(url);
        // 如果接受端是json形式，必须指定Content-Type为json
        httpPost.setHeader("Content-Type", "application/json;charset=utf8");
        httpPost.setEntity(stringEntity);
        try {
            response = httpClient.execute(httpPost);
            return EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (null != response) {
                    response.close();
                }
                if (null != httpClient) {
                    httpClient.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}