package flink.util;
import flink.common.ConfigurationManager;
import flink.constant.Constants;
import flink.function.EventFlatMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.File;
import java.io.InputStream;
public class IpUtils {
    private static Logger log = LoggerFactory.getLogger(IpUtils.class);
    /**
     * 创建ip2region文件
     * @return
     */
    public static String createFtlFileByFtlArray() {
        String ftlPath = "ipdb/";
        return createFtlFile(ftlPath, "ip2region.xdb");
    }
    /**
     * 创建文件
     * @param ftlPath
     * @param ftlName
     * @return
     */
    private static String createFtlFile(String ftlPath, String ftlName) {
        InputStream certStream = null;
        try {
            //获取当前项目所在的绝对路径
            String proFilePath = System.getProperty("user.dir");
            //获取模板下的路径，然后存放在temp目录下　
            String newFilePath = proFilePath + File.separator + "temp" + File.separator + ftlPath;
            newFilePath = newFilePath.replace("/", File.separator);

            log.error("newFilePath : " + newFilePath + ftlName);
            //检查项目运行时的src下的对应路径
            File newFile = new File(newFilePath);
            if (newFile.isFile() && newFile.exists()) {
                return newFilePath;
            }
            //当项目打成jar包会运行下面的代码，并且复制一份到src路径下（具体结构看下面图片）
            certStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(ftlPath + ftlName);
            byte[] certData = org.apache.commons.io.IOUtils.toByteArray(certStream);
            org.apache.commons.io.FileUtils.writeByteArrayToFile(newFile, certData);
            newFilePath = newFilePath + ftlName;
            log.error("newFilePath final : " + newFilePath);
            return newFilePath;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            try {
                certStream.close();
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        return null;
    }
}