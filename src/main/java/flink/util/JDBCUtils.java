package flink.util;

import com.alibaba.druid.pool.DruidDataSource;
import flink.common.ConfigurationManager;
import flink.constant.DBconfig;
import org.apache.flink.api.java.tuple.Tuple2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.Properties;

/**
 * @version 1.0
 * <AUTHOR>
 * @create 2021/8/26 17:54
 */
public class JDBCUtils {

    private static Logger log = LoggerFactory.getLogger(JDBCUtils.class);
    public static DruidDataSource initDruidConnection(ConfigurationManager config, String type) throws ClassNotFoundException {
        DruidDataSource dataSource = null;
        switch (type) {
            case "MYSQL":
                Class.forName(DBconfig.MYSQL_DRIVER);
                break;
            case "PSQL":
                Class.forName(DBconfig.PSQL_DRIVER);
                break;
        }
        try {
            dataSource = DruidUtils.init(config, type);
        } catch (Exception e) {
            log.error("GET JDBC CONNECTION FAILED.");
            log.error(e.getMessage(), e);
        }
        return dataSource;
    }


    public static Connection initConnection(String dbType, String url, String user, String password) throws ClassNotFoundException {
        Connection connection = null;
        Properties prop = new Properties();
        switch (dbType) {
            case "MYSQL":
                Class.forName(DBconfig.MYSQL_DRIVER);
                break;
            case "PSQL":
                Class.forName(DBconfig.PSQL_DRIVER);
                break;
            case "HIVE":
                Class.forName(DBconfig.HIVE_DRIVER);
                prop.setProperty("user",user);
                prop.setProperty("password",password);
                prop.setProperty("incremental","true");
                prop.setProperty("incrementalBufferRows", "1000000");
                break;
            default:
                throw new IllegalStateException("Unexpected value: " + dbType);
        }
        try {
            if(prop == null || prop.getProperty("user") == null){
                connection = DriverManager.getConnection(url, user, password);
            }else{
                connection = DriverManager.getConnection(url, prop);
            }
        } catch (Exception e) {
            log.error("GET JDBC CONNECTION FAILED");
            log.error(e.getMessage(), e);
        }
        return connection;
    }


    /**
     * 返回单个String数据
     * @param type  数据库类型，mysql，psql
     * @param url
     * @param user
     * @param password
     * @param tableName
     * @param colName
     * @param colNameAndValue 是否将结果字段转换成驼峰式
     * @return
     */
    public static String queryOneValue(ConfigurationManager config, String type, String tableName, String colName, Tuple2<String, String>... colNameAndValue) {
        DruidDataSource dataSource = null;
        Connection connection = null;
        PreparedStatement ps;
        ResultSet rs;
        String res = null;
        String condition = " where ";

        try {
            dataSource = initDruidConnection(config, type);
            connection = dataSource.getConnection();
            for (int i = 0; i < colNameAndValue.length; i++) {
                Tuple2<String, String> tuple2 = colNameAndValue[i];
                String fileName = tuple2.f0;
                String fileValue = tuple2.f1;

                // 多条件查询
                if (i > 0) {
                    condition += " and ";
                }
                condition += fileName + "='" + fileValue + "'";
            }
        } catch (Exception e) {
            log.error("PARSE CONDITION FAILED." + e);
        }
        String sql = "select " + colName + " from " + tableName + condition;
        log.info(sql);
        try {
            ps = connection.prepareStatement(sql);
            rs = ps.executeQuery();
            while (rs.next()) {
                res = rs.getString(1);
            }
        } catch (SQLException e) {
            log.info(sql);
            log.error(String.format("QUERY JDBC ABOUT %s FAILED.", colName));
        }
        return res;
    }

    /**
     * 添加数据库表字段
     * @param dbType：数据库类型，MySQL，postgresSQL
     * @param url
     * @param user
     * @param password
     * @param tableName：表名
     * @param colName：添加的字段名
     * @param colType：添加的字段类型
     * @throws ClassNotFoundException
     */
    public static void ModifyColumnWithType(String dbType, String url, String user, String password, String tableName, String colName, String colType) throws ClassNotFoundException {
        PreparedStatement preparedStatement = null;

        if ("varchar".equals(colType)) {
            colType += "(255)";
        }

        String sql = "alter table " + tableName + " add column " + "`" + colName + "` " + colType + " after member_id";
        Connection connection = initConnection(dbType, url, user, password);

        try {
            preparedStatement = connection.prepareStatement(sql);
            preparedStatement.executeUpdate(sql);
        } catch (SQLException e) {
            log.error("SQL RUN FAILED.\n" + sql);
            log.error(e.getMessage(), e);
        } finally {
            closeConenction(connection, preparedStatement);
        }
    }


    public static void closeConenction(Connection connection, Statement statement) {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        if (statement != null) {
            try {
                statement.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }
}
