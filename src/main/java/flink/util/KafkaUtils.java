package flink.util;

import com.alibaba.fastjson.JSONObject;
import flink.common.ConfigurationManager;
import flink.constant.Constants;
import flink.model.EventModel;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaConsumerBase;
import org.apache.flink.streaming.connectors.kafka.FlinkKafkaProducer;
import org.apache.flink.streaming.connectors.kafka.partitioner.FlinkKafkaPartitioner;
import org.apache.flink.streaming.util.serialization.KeyedSerializationSchema;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.PartitionInfo;
import org.apache.kafka.common.TopicPartition;
import org.checkerframework.checker.units.qual.C;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.apache.kafka.clients.producer.ProducerConfig.*;

public class KafkaUtils {

    static Logger log = LoggerFactory.getLogger(KafkaUtils.class);
    static Properties propKafka;
    static Properties propSensorKafka;
    static Properties propBasic = new Properties();

    public static void assignOffsetTimeBefore(String topic,ConfigurationManager config){
        if(propKafka == null){
            propKafka = initProp(config);
        }
        //        props.put("bootstrap.servers", "kafka1:9092");
//        props.put("group.id", "test");
//        props.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
//        props.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        KafkaConsumer<String, String> consumer = new KafkaConsumer<>(propKafka);

        try {
            // 获取topic的partition信息
            List<PartitionInfo> partitionInfos = consumer.partitionsFor(topic);
            List<TopicPartition> topicPartitions = new ArrayList<>();

            Map<TopicPartition, Long> timestampsToSearch = new HashMap<>();
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date now = new Date();
            long nowTime = now.getTime();
            System.out.println("当前时间: " + df.format(now));

            long fetchDataTime = nowTime - 1000 * 60 * 30;  // 计算30分钟之前的时间戳

            for (PartitionInfo partitionInfo : partitionInfos) {
                topicPartitions.add(new TopicPartition(partitionInfo.topic(), partitionInfo.partition()));
                timestampsToSearch.put(new TopicPartition(partitionInfo.topic(), partitionInfo.partition()), fetchDataTime);
            }

            consumer.assign(topicPartitions);

            // 获取每个partition一个小时之前的偏移量
            Map<TopicPartition, OffsetAndTimestamp> map = consumer.offsetsForTimes(timestampsToSearch);

            OffsetAndTimestamp offsetTimestamp = null;
            System.out.println("开始设置各分区初始偏移量...");
            for (Map.Entry<TopicPartition, OffsetAndTimestamp> entry : map.entrySet()) {
                // 如果设置的查询偏移量的时间点大于最大的索引记录时间，那么value就为空
                offsetTimestamp = entry.getValue();
                if (offsetTimestamp != null) {
                    int partition = entry.getKey().partition();
                    long timestamp = offsetTimestamp.timestamp();
                    long offset = offsetTimestamp.offset();
                    System.out.println("partition = " + partition +
                            ", time = " + df.format(new Date(timestamp)) +
                            ", offset = " + offset);
                    // 设置读取消息的偏移量
                    consumer.seek(entry.getKey(), offset);
                }
            }
            System.out.println("设置各分区初始偏移量结束...");

            while (true) {
                ConsumerRecords<String, String> records = consumer.poll(1000);
                for (ConsumerRecord<String, String> record : records) {
                    System.out.println("partition = " + record.partition() + ", offset = " + record.offset());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            consumer.close();
        }
    }

    public static Properties initProp(ConfigurationManager config){
        if(propKafka == null){
            propKafka = new Properties();
        }
        propKafka.setProperty(FlinkKafkaConsumerBase.KEY_PARTITION_DISCOVERY_INTERVAL_MILLIS, "10000");
//        InputStream resourceAsStream = sdp2mysql.class.getClassLoader().getResourceAsStream("prod.properties");
//        try {
//            propBasic.load(resourceAsStream);
//        } catch ( IOException e) {
//            System.out.println(e.getMessage());
//            e.printStackTrace();
//        }
        propKafka.setProperty(ACKS_CONFIG, "-1");
        propKafka.setProperty(KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        propKafka.setProperty(VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.ByteArrayDeserializer");

        //RecordAccumulator缓冲区大小32M
        propKafka.setProperty(BUFFER_MEMORY_CONFIG, "33554432");

        //131KB
        propKafka.setProperty(BATCH_SIZE_CONFIG, "131072");

        //100ms
        propKafka.setProperty(LINGER_MS_CONFIG, "100");

        propKafka.setProperty(MAX_REQUEST_SIZE_CONFIG, "83886080");
        propKafka.setProperty(RETRIES_CONFIG, "10");
        propKafka.setProperty(RETRY_BACKOFF_MS_CONFIG, "500");
        propKafka.setProperty(TRANSACTION_TIMEOUT_CONFIG, 60 * 5 * 1000 + "");

        // b-3.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092,b-2.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092,b-1.prod-lb-bi.lgtp61.c3.kafka.ap-east-1.amazonaws.com:9092
        propKafka.setProperty(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, config.getString(Constants.KAFKA_EVENT_SOURCE_BOOTSTRAP_SERVERS));
        propKafka.setProperty(ConsumerConfig.GROUP_ID_CONFIG, config.getString(Constants.JOB_NAME) + config.getString(Constants.MY_ENV) + "group");
        propKafka.setProperty(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
//        propKafka.setProperty(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        return propKafka;
    }

    public static KafkaSink KafkaSinkExactlyOnce(ConfigurationManager config) {
        Properties prop = new Properties();
        UUID transactionID = UUID.randomUUID();
        prop.setProperty("transaction.timeout.ms", "900000");
        KafkaSink<String> sink = KafkaSink.<String>builder()
                .setKafkaProducerConfig(prop)
                .setBootstrapServers(config.getString(Constants.KAFKA_EVENT_SINK_BOOTSTRAP_SERVERS))
                .setRecordSerializer(KafkaRecordSerializationSchema.builder()
                        .setTopic(config.getString(Constants.KAFKA_EVENT_SINK_TOPIC))
                        .setPartitioner(new FlinkKafkaPartitioner<String>() {
                            @Override
                            public int partition(String record, byte[] key, byte[] value, String targetTopic, int[] partitions) {
                                JSONObject data = JSONObject.parseObject(record);
                                String distinctId = data.getString("distinct_id") == null ? "1" : data.getString("distinct_id");
//                                String distinctId = record.getDistinct_id();
                                return Math.abs(distinctId.hashCode()) % partitions.length;
                            }
                        })
                        .setValueSerializationSchema(new SimpleStringSchema())
                        .build()
                )
                .setDeliverGuarantee(DeliveryGuarantee.EXACTLY_ONCE)
//                .setDeliverGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                .setTransactionalIdPrefix(transactionID.toString())
                .build();


        return sink;
    }

    public static KafkaSource<JSONObject> getSourceWithOffsetRepair(ConfigurationManager config) {
        Properties prop = initProp(config);
//        prop.setProperty("max.partition.fetch.bytes", "52428800");
//        prop.setProperty("fetch.message.max.bytes", "52428800");
        return KafkaSource.<JSONObject>builder()
                .setBootstrapServers(config.getString(Constants.KAFKA_EVENT_SOURCE_BOOTSTRAP_SERVERS))
                .setTopics(config.getString(Constants.KAFKA_EVENT_REPAIR_TOPIC))
                .setGroupId(config.getString(Constants.KAFKA_EVENT_GROUP))
                .setProperties(prop)
                .setStartingOffsets(OffsetsInitializer.latest())
                .setDeserializer(new KafkaRecordDeserializationSchema<JSONObject>() {
                                     @Override
                                     public TypeInformation<JSONObject> getProducedType() {
                                         return TypeInformation.of(JSONObject.class);
                                     }
                                     @Override
                                     public void deserialize(ConsumerRecord<byte[], byte[]> record, Collector<JSONObject> out) throws IOException {
                                         String jsonString = new String(record.value(), StandardCharsets.UTF_8);
                                         try {
                                             JSONObject jsonObject = JSONObject.parseObject(jsonString);
                                             out.collect(jsonObject);
                                         } catch (Exception e) {
                                             log.error(e.getMessage(), e);
//                                             model.Alert.slackAlert(true, "json新增offset异常报错\nKAFKA_OFFSET_KEY : " + record.offset(), "线上神策异常数据监控群");
                                             model.Alert.sensorAlert(config.getString(Constants.feishu_URL), "json新增offset异常报错", "KAFKA_OFFSET_KEY : " + record.offset());
                                         }
                                     }
                                 }
                ).build();
    }

    public static KafkaSource<JSONObject> getSourceWithOffset(ConfigurationManager config) {
//        StringBuilder sb = new StringBuilder();
//        String[] split = groupId.replaceAll("-", "_").split("_");
//        for (String s : split) {
//            sb.append(s, 0, 1);
//        }
//        String simpleGroupId = sb.toString();
        Properties prop = initProp(config);
//        prop.setProperty("max.partition.fetch.bytes", "52428800");
//        prop.setProperty("fetch.message.max.bytes", "52428800");
        return KafkaSource.<JSONObject>builder()
                .setBootstrapServers(config.getString(Constants.KAFKA_EVENT_SOURCE_BOOTSTRAP_SERVERS))
                .setTopics(config.getString(Constants.KAFKA_EVENT_SOURCE_TOPIC))
                .setGroupId(config.getString(Constants.KAFKA_EVENT_GROUP))
                .setProperties(prop)
                .setStartingOffsets(OffsetsInitializer.latest())
//                .setStartingOffsets(OffsetsInitializer.timestamp(1717344000000l))
                .setDeserializer(new KafkaRecordDeserializationSchema<JSONObject>() {
                                     @Override
                                     public TypeInformation<JSONObject> getProducedType() {
                                         return TypeInformation.of(JSONObject.class);
                                     }
                                     @Override
                                     public void deserialize(ConsumerRecord<byte[], byte[]> record, Collector<JSONObject> out) throws IOException {
                                         String jsonString = new String(record.value(), StandardCharsets.UTF_8);
                                         try {
                                             JSONObject jsonObject = JSONObject.parseObject(jsonString);
                                             jsonObject.put(Constants.KAFKA_OFFSET_KEY, record.offset());
                                             jsonObject.put(Constants.KAFKA_PARTITION_KEY, record.partition());
                                             jsonObject.put(Constants.KAFKA_GROUP_KEY, config.getString(Constants.KAFKA_EVENT_GROUP));
                                             out.collect(jsonObject);
                                         } catch (Exception e) {
                                             log.error(e.getMessage(), e);
//                                             model.Alert.slackAlert(true, "json新增offset异常报错\nKAFKA_OFFSET_KEY :"  + record.offset(), "线上神策异常数据监控群");
                                             model.Alert.sensorAlert(config.getString(Constants.feishu_URL), "json新增offset异常报错", "KAFKA_OFFSET_KEY : " + record.offset());
                                         }
                                     }
                                 }
                ).build();
    }

    /**
     * @param brokers
     * @param topic
     * @param groupId
     * @return ConsumerRecord(topic = lb - psql - cdc - ods, partition = 0, leaderEpoch = 0, offset = 4656599, CreateTime = 1650588022463, serialized key size = - 1, serialized value size = 616, headers = RecordHeaders ( headers = [], isReadOnly = false), key = null, value = [B@8803749
     */
    public static KafkaSource<String> kafkaSource(String brokers, String topic, String groupId) {
        Properties properties = setConsumerBaseProperties();
        KafkaSource<String> kafkaSource = KafkaSource.<String>builder()
                .setBootstrapServers(brokers)
                .setTopics(topic)
                .setGroupId(groupId)
                .setStartingOffsets(OffsetsInitializer.earliest())
//                .setStartingOffsets(OffsetsInitializer.committedOffsets()) // checkpoint后在提交offset，即oncheckpoint模式，该值默认为true
//                .setDeserializer(KafkaRecordDeserializationSchema.valueOnly(StringDeserializer.class))
                .setDeserializer(new KafkaRecordDeserializationSchema<String>() {
                    @Override
                    public void deserialize(ConsumerRecord<byte[], byte[]> record, Collector<String> out) throws IOException {
                        //todo 输出日志待优化
                        if (record == null) {
                        } else {
                            out.collect(new String(record.value(), StandardCharsets.UTF_8));
                        }
                    }

                    @Override
                    public TypeInformation<String> getProducedType() {
                        return TypeInformation.of(String.class);
                    }
                })
                .setProperties(properties)
                .build();
        return kafkaSource;
    }

    public static Properties setConsumerBaseProperties() {
        Properties properties = new Properties();
//        properties.setProperty(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, String.valueOf(new SimpleStringSchema()));
//        properties.setProperty("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
//        properties.setProperty("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        properties.setProperty(ConsumerConfig.ISOLATION_LEVEL_CONFIG, "read_committed");
        properties.setProperty(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, "false");
        properties.setProperty("partition.discovery.interval.ms", "10000");
        return properties;
    }

    public static FlinkKafkaProducer<EventModel> getSensorFlinkKafkaProducer(ConfigurationManager config) {
        Properties properties = new Properties();
        properties.setProperty("bootstrap.servers", config.getString(Constants.KAFKA_HK_HOST));
        properties.setProperty("buffer.memory", "67108864");
        properties.setProperty("batch.size", "131072");
        properties.setProperty("linger.ms", "100");
        properties.setProperty("max.request.size", "10485760");
        properties.setProperty(ProducerConfig.ACKS_CONFIG, "1");
        properties.setProperty("retries", "10");
        properties.setProperty("retry.backoff.ms", "500");
//        properties.setProperty("transaction.timeout.ms", "600000");
//        properties.setProperty(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, "true");
        FlinkKafkaProducer<EventModel> KafkaProducer = new FlinkKafkaProducer<>(
                config.getString(Constants.KAFKA_EVENT_SINK_TOPIC),
                (KeyedSerializationSchema) KafkaRecordSerializationSchema.builder()
                        .setPartitioner(new FlinkKafkaPartitioner<String>() {
                            @Override
                            public int partition(String record, byte[] key, byte[] value, String targetTopic, int[] partitions) {
                                JSONObject data = JSONObject.parseObject(record);
                                String distinctId = data.getString("distinct_id") == null ? "1" : data.getString("distinct_id");
//                                String distinctId = record.getDistinct_id();
                                return Math.abs(distinctId.hashCode()) % partitions.length;
                            }
                        })
                        .setValueSerializationSchema(new SimpleStringSchema())
                        .build(),
                properties,
                FlinkKafkaProducer.Semantic.EXACTLY_ONCE);
        return KafkaProducer;
    }
}

