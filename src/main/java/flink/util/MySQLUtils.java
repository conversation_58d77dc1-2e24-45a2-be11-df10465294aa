package flink.util;

import flink.common.ConfigurationManager;
import flink.conf.DBconfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;

/**
 * @version 1.0
 * @<PERSON> <PERSON><PERSON><PERSON>
 * @create 2021/8/26 17:54
 */
public class MySQLUtils {


    static Connection connection = null;
    private static Logger log = LoggerFactory.getLogger(MySQLUtils.class);


    public static Connection initConnection(ConfigurationManager config, String DBname) {

        if(connection != null){
            return connection;
        }else{

            String MYSQL_URL = config.getString("mysql-bi-sdp.url");
            String MYSQL_USER = config.getString("mysql-bi-sdp.user");
            String MYSQL_PASSWORD = config.getString("mysql-bi-sdp.password");

            try {
                Class.forName(DBconfig.MYSQL_DRIVER);
                connection = DriverManager.getConnection(MYSQL_URL, MYSQL_USER, MYSQL_PASSWORD);
//            connection = MysqlDruidUtils.init(MYSQL_URL, MYSQL_USER, MYSQL_PASSWORD).getConnection();
            } catch (ClassNotFoundException e) {
                e.printStackTrace();
            } catch (SQLException e) {
                e.printStackTrace();
            }
            return connection;
        }
    }


    /**
     * 返回单个String数据
     * @param prop
     * @param tableName
     * @param colName
     * @param colNameAndValue
     * @return
     */
//    public static String queryOneValue(ConfigurationManager prop, String tableName, String colName, Tuple2<String, String>... colNameAndValue) {
//        Connection connection = null;
//        PreparedStatement ps;
//        ResultSet rs;
//        String res = null;
//        String condition = " where ";
//
//        try {
//            connection = initConnection();
//            for (int i = 0; i < colNameAndValue.length; i++) {
//                Tuple2<String, String> tuple2 = colNameAndValue[i];
//                String fileName = tuple2.f0;
//                String fileValue = tuple2.f1;
//
//                // 多条件查询
//                if (i > 0) {
//                    condition += " and ";
//                }
//                condition += fileName + "='" + fileValue + "'";
//            }
//        } catch (Exception e) {
//            log.error("PARSE CONDITION FAILED." + e);
//        }
//        String sql = "select " + colName + " from " + tableName + condition;
//        try {
//            ps = connection.prepareStatement(sql);
//            rs = ps.executeQuery();
//            while (rs.next()) {
//                res = rs.getString(1);
//            }
//        } catch (SQLException e) {
//            log.error(sql);
//            log.error(String.format("QUERY MYSQL ABOUT %s FAILED.", colName));
//        }
//        return res;
//    }
//
//
//    public static <T> List<T> queryList(ConfigurationManager prop, String tableName, List colName, JSONObject condition, Class<T> clazz, Boolean underScoreToCamel) {
//        Connection connection = null;
//        PreparedStatement ps = null;
//        ResultSet rs = null;
//        String conditionParse = "";
//        String colNameParse = "";
//
//        // 解析条件
//        try {
//            if (condition != null) {
//                conditionParse = " where ";
//                Set<Map.Entry<String, Object>> entries = condition.entrySet();
//                Iterator<Map.Entry<String, Object>> iterator = entries.iterator();
//                while (iterator.hasNext()) {
//                    Map.Entry<String, Object> next = iterator.next();
//                    String key = next.getKey();
//                    Object value = next.getValue();
//                    conditionParse += key + "='" + value + "'";
//                }
//            }
//        } catch (Exception e) {
//            log.error(e.getMessage());
//            System.out.println("PARSE CONDITION FAILED: " + conditionParse);
//        }
//
//        try {
//            for (int i = 0; i < colName.size(); ++i) {
//                colNameParse += colName.get(i);
//                if (i < colName.size() - 1 ) {
//                    colNameParse +=",";
//                }
//            }
//        } catch (Exception e) {
//            log.error(e.getMessage());
//            System.out.println("PARSE COLNAME FAILED: " + colNameParse);
//        }
//
//        // 拼接SQL
//        String sql = "select " + colNameParse + " from " + tableName + conditionParse;
//
//        try {
//            connection = initConnection();
//            ps = connection.prepareStatement(sql);
//            log.info(sql);
//
//            //执行SQL语句
//            try {
//                rs = ps.executeQuery();
//            } catch (SQLException e) {
//                e.printStackTrace();
//                throw new RuntimeException("SQL RUN WITH ERROR.");
//            }
//
//            // 处理结果集
//            ResultSetMetaData md = null;
//            try {
//                md = rs.getMetaData();
//            } catch (SQLException e) {
//                e.printStackTrace();
//                throw new RuntimeException("GET SQL RESULT ERROR.");
//            }
//
//            //声明集合对象，用于封装返回结果
//            List<T> resultList = new ArrayList<T>();
//
//            try {
//                //每循环一次，获取一条查询结果
//                while (rs.next()) {
//                    //通过反射创建要将查询结果转换为目标类型的对象
//                    T obj = clazz.newInstance();
//
//                    //对查询出的列进行遍历，每遍历一次得到一个列名
//                    for (int i = 1; i <= md.getColumnCount(); i++) {
//                        String propertyName = md.getColumnName(i);
//                        //如果开启了下划线转驼峰的映射，那么将列名里的下划线转换为属性的打
//                        if (underScoreToCamel) {
//                            //直接调用Google的guava的CaseFormat  LOWER_UNDERSCORE小写开头+下划线->LOWER_CAMEL小写开头+驼峰
//                            propertyName = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, propertyName);
//                        }
//                        //调用apache的commons-bean中的工具类，给Bean的属性赋值
//                        BeanUtils.setProperty(obj, propertyName, rs.getObject(i));
//                    }
//                    resultList.add(obj);
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//                throw new RuntimeException("ADD ERROR");
//            }
//            return resultList;
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw new RuntimeException("READ CONFIG FROM MYSQL FAILED.");
//        } finally {
//            if(rs != null){
//                try {
//                    rs.close();
//                } catch (SQLException e) {
//                    e.printStackTrace();
//                }
//            }
//            if(ps != null){
//                try {
//                    ps.close();
//                } catch (SQLException e) {
//                    e.printStackTrace();
//                }
//            }
//            if(connection != null){
//                try {
//                    connection.close();
//                } catch (SQLException e) {
//                    e.printStackTrace();
//                }
//            }
//        }
//    }
}
