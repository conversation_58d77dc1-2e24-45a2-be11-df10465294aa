package flink.util;


import flink.common.ConfigurationManager;
import flink.conf.DBconfig;
import flink.constant.Constants;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

/**
 * @version 1.0
 * <AUTHOR>
 * @create 2021/9/25 22:11
 */
public class PhoenixUtils {
    private static Logger log = LoggerFactory.getLogger(PhoenixUtils.class);

    private static ConfigurationManager prop;
    public static Connection connection = null;

    /**
     * 根据sever地址获取链接
     *
     * @param server 地址
     * @return Connection
     */
    public static Connection getConnection(String server) {
        try {
            Properties properties = new Properties();
            properties.setProperty("phoenix.schema.mapSystemTablesToNamespace", "true");
            properties.setProperty("phoenix.schema.isNamespaceMappingEnabled", "true");
            Class.forName(DBconfig.PHOENIX_DRIVER);
            connection = DriverManager.getConnection(server, properties);
        } catch (ClassNotFoundException e) {
            System.out.println(e.getMessage());
            log.error("PhoenixQueryServerDriver NOT FOUND\n" + e);
        } catch (SQLException e) {
            System.out.println(e.getMessage());
            log.error("CONNECTION PHOENIX ERROR\n" + e);
        }
        return connection;
    }

    public static Connection getConnectionTest(String server) throws SQLException, ClassNotFoundException {
            Properties properties = new Properties();
            properties.setProperty("phoenix.schema.mapSystemTablesToNamespace", "true");
            properties.setProperty("phoenix.schema.isNamespaceMappingEnabled", "true");
            Class.forName(DBconfig.PHOENIX_DRIVER);
            connection = DriverManager.getConnection(server, properties);
        return connection;
    }


    public static <T> List<T> queryList(ConfigurationManager prop, String sql, Class<T> claszz) {
        if (connection == null) {
            try {
                connection = getConnection(prop.getString(Constants.PHOENIX_SERVER));
            } catch (Exception e) {
                log.error("CONNECT HBASE FAILED.\n" + e);
            }
        }

        List<T> resultList = new ArrayList<>();
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;
        ResultSetMetaData metaData = null;

        try {
            preparedStatement = connection.prepareStatement(sql);
        } catch (SQLException e) {
            log.warn("CONNECT HBASE FAILED.\n" + e);
        }

        try {
            resultSet = preparedStatement.executeQuery();
            metaData = resultSet.getMetaData();
        } catch (SQLException e) {
            log.error(sql);
            log.warn("GET META DATA FROM Hbase FAILED.\n" + e);
        }

        try {
            while (resultSet.next()) {
                T rowData = claszz.newInstance();
                for (int i = 1; i <= metaData.getColumnCount(); i++) {
                    BeanUtils.setProperty(rowData, metaData.getColumnName(i), resultSet.getObject(i));
                }
                resultList.add(rowData);
            }
        } catch (Exception e) {
            log.error("PARSE HBASE RESULT FAILED\n" + e);
        } finally {
            if (resultList != null) {
                try {
                    resultSet.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            if (preparedStatement != null) {
                try {
                    preparedStatement.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
//            if (connection != null) {
//                try {
//                    connection.close();
//                } catch (SQLException e) {
//                    e.printStackTrace();
//                }
//            }
        }
        return resultList;
    }

    public static void deleteSql(ConfigurationManager prop, String sql) {
        if (connection == null) {
            connection = getConnection(prop.getString(Constants.PHOENIX_SERVER));
        }
        PreparedStatement preparedStatement = null;

        try {
            preparedStatement = connection.prepareStatement(sql);
            preparedStatement.executeQuery();
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            if (preparedStatement != null) {
                try {
                    preparedStatement.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
