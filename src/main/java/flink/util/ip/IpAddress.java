package flink.util.ip;////
//// Source code recreated from a .class file by IntelliJ IDEA
//// (powered by FernFlower decompiler)
////
//
//package flink.util.ip;
//
//import com.exception.MyException;
//import java.io.ByteArrayOutputStream;
//import java.io.IOException;
//import java.io.InputStream;
//import java.io.Serializable;
//import java.math.BigInteger;
//import java.util.ArrayList;
//import java.util.List;
//import org.apache.commons.lang.StringUtils;
//
//public class IpAddress implements Serializable {
//    public static final int HEXTETCOUNT = 8;
//    public static long firstIndex = 0L;
//    public static long indexCount = 0L;
//    public static long offlen = 0L;
//    public static long iplen = 0L;
//    public static byte[] data;
//
//    public IpAddress() {
//    }
//
//    public static void init() throws IOException {
//        InputStream is = IpAddress.class.getClassLoader().getResourceAsStream("ipv6wry.db");
//        data = readAllAsStream(is);
//        firstIndex = longFrom8Bytes(16, 8);
//        indexCount = longFrom8Bytes(8, 8);
//        offlen = longFrom8Bytes(6, 1);
//        iplen = longFrom8Bytes(7, 1);
//    }
//
//    protected static byte[] readAllAsStream(InputStream in) throws IOException {
//        ByteArrayOutputStream out = new ByteArrayOutputStream();
//        byte[] buffer = new byte[4096];
//
//        int n;
//        while((n = in.read(buffer)) != -1) {
//            out.write(buffer, 0, n);
//        }
//
//        in.close();
//        return out.toByteArray();
//    }
//
//    public String getIPAddress(String address) throws MyException, IOException {
//        BigInteger ip6 = getIntIpFromString(address);
//        Long ip = 0L;
//        ip = ip6.shiftRight(64).longValue() & -1L;
//        long index = find(ip, 0, (int)indexCount);
//        long ip_off = firstIndex + index * (8L + offlen);
//        long ip_rec_off = longFrom8Bytes((int)ip_off + 8, (int)offlen);
//        return getAddress((int)ip_rec_off);
//    }
//
//    public static long find(Long ip, int l, int r) throws IOException {
//        if (r - l <= 1) {
//            return (long)l;
//        } else {
//            int middle = (l + r) / 2;
//            long o = firstIndex + (long)middle * (8L + offlen);
//            long newIP = longFrom8Bytes((int)o, 8);
//            return ip < newIP ? find(ip, l, middle) : find(ip, middle, r);
//        }
//    }
//
//    public static long longFrom8Bytes(int offset, int size) {
//        long value = 0L;
//
//        for(int count = 0; count < size; ++count) {
//            int shift = count << 3;
//            value |= 255L << shift & (long)data[offset + count] << shift;
//        }
//
//        return value;
//    }
//
//    public static BigInteger getIntIpFromString(String address) throws MyException {
//        if (!StringUtils.contains(address, ":")) {
//            throw new MyException("it's not a ipv6");
//        } else {
//            String[] addArr = address.split(":");
//            if (StringUtils.equals(addArr[0], "") || addArr.length == 8 && StringUtils.equals(addArr[7], "") || addArr.length == 7 && StringUtils.equals(addArr[6], "")) {
//                throw new MyException("Can't have '::' at start or end point");
//            } else {
//                List<Integer> list = new ArrayList();
//                Integer partsHi = addArr.length;
//                Integer partsLo = 0;
//                Integer partsSkipped = 0;
//
//                for(int i = 0; i < addArr.length; ++i) {
//                    if (StringUtils.isBlank(addArr[i])) {
//                        list.add(i);
//                    }
//                }
//
//                if (list.size() > 1) {
//                    throw new MyException("Can't have more than one '::'");
//                } else {
//                    if (list.size() == 1) {
//                        partsHi = (Integer)list.get(0);
//                        partsLo = addArr.length - (Integer)list.get(0) - 1;
//                        partsSkipped = 8 - (partsHi + partsLo);
//                    }
//
//                    BigInteger bigInt = BigInteger.ZERO;
//
//                    int i;
//                    for(i = 0; i < partsHi; ++i) {
//                        bigInt = bigInt.shiftLeft(16);
//                        bigInt = bigInt.or(BigInteger.valueOf((long)parseHextet(addArr[i])));
//                    }
//
//                    bigInt = bigInt.shiftLeft(16 * partsSkipped);
//
//                    for(i = addArr.length - partsLo; i < addArr.length; ++i) {
//                        bigInt = bigInt.shiftLeft(16);
//                        bigInt = bigInt.or(BigInteger.valueOf((long)parseHextet(addArr[i])));
//                    }
//
//                    return bigInt;
//                }
//            }
//        }
//    }
//
//    private static int parseHextet(String hextet) {
//        return Integer.parseInt(hextet, 16);
//    }
//
//    public static String getAddress(int offset) {
//        byte b = data[offset];
//        if (b == 1) {
//            return getAddress((int)longFrom8Bytes(offset + 1, (int)offlen));
//        } else {
//            String areaAddr = getAreaAddr(offset);
//            int o;
//            if (b == 2) {
//                o = (int)((long)offset + 1L + offlen);
//            } else {
//                o = find(offset) + 1;
//            }
//
//            String operatorAddress = getAreaAddr(o);
//            return areaAddr + "-" + operatorAddress;
//        }
//    }
//
//    public static String getAreaAddr(int offset) {
//        byte b = data[offset];
//        return b != 1 && b != 2 ? getString(offset) : getAreaAddr((int)longFrom8Bytes(offset + 1, (int)offlen));
//    }
//
//    public static String getString(int offset) {
//        int size = find(offset) - offset;
//        return new String(data, offset, size);
//    }
//
//    public static int find(int offset) {
//        int o1 = data.length;
//
//        for(int i = offset; i < data.length; ++i) {
//            if (data[i] == 0) {
//                o1 = i;
//                break;
//            }
//        }
//
//        return o1;
//    }
//
//    public static void main(String[] args) {
//        try {
//            init();
//            String ip = "2408:84ed:73:1534:6328:f5d8:f2ac:1757,240e:ff:b013:8453:f4fc:7353:40a3:c744,240e:ac:e035:b0ee:6863:5bf1:5670:c8,2408:84ed:cc:324d:d5a3:43f:b50b:1e4b,240e:470:274:4c11:a86d:c94f:ff51:bad0,2408:8207:18a4:e5e0:1d8e:1823:9b2:1308,2408:84f3:416:70d5:d67:b8be:93fd:3fe,2408:84f7:96:ea33:f5ae:c757:6553:a460,2408:84ed:76:8337:49ad:ef0b:9f0e:8a8d,2408:84ed:72:51a9:bd8c:658d:1133:529f,240e:470:230:f34d:3c7c:ead2:f1b3:bee1,2408:84ed:52:ce8f:e1a8:d1a4:cb2c:57a2,2408:84ed:39:9c04:e191:65fe:77fa:7f21,2400:2412:8a0:b800:98ba:54cd:2ccb:729b,fe80::1800:6cc4:c96e:ed04,2408:84f3:8e04:e26b:5898:ed46:f08b:fd61,2408:805f:e306:f6b0:cd03:2a84:3f37:1d70,240e:470:31:75b0:8e9:40f3:3640:7193,240e:470:347:fb5c:84d:4d29:bafc:339f,240e:470:38:e571:5939:1183:fb6c:1db6,240e:b0:287:7080:9dd1:e36e:5726:da1b,240e:e0:f885:d31d:5de5:c2a0:1dcd:1c56,2408:84e1:105:d1d8:25ac:d10a:b0b5:ae3c,240e:470:38:e571:a1b4:e7e5:d87c:be0f,2408:805f:e320:7d0:7519:dc91:7d40:9a9b,240e:e0:f82c:3ad7:e8ca:9b8d:aa11:40d6,2408:8207:18a0:5480:d194:ab4a:35ec:d3bc,240e:470:329:dc2c:402b:8b88:ea78:1c0e,2408:84f3:4c10:937b:c92b:c784:ad0e:d29c,2408:84e1:cf:80c8:a9d8:3dc7:d16:9a27,2001:e68:540e:1073:98c6:9631:e2c9:d789,2408:84f3:3a01:d1ed:dcf2:1cdb:e399:6c25,2408:84ed:76:8337:279c:9b8f:7966:b419,2408:84ed:44:9c55:e1f2:6140:321c:cf5c,2408:84f3:81f:6e01:5933:4a35:4d2f:96bd,2408:84ed:74:cb85:9196:6ff1:d52a:73a9,2408:84e1:12d:1f9:a47d:1ea1:acec:a1bc,2408:84ed:27:7e2e:f44f:4d59:dc06:3e62,2408:84ed:27:37e:ad88:732a:419e:d8c5,2408:84ed:20:e587:d1dc:b9d3:fb6f:429b,2408:84e1:a0:982:2133:a226:f230:f21b,2408:84ed:47:3119:1898:6ab:963c:b666,2408:84f3:740b:c876:dd8f:2c0a:68d3:8fa0,2408:84ed:77:de57:618f:8206:4be1:a896,2408:84f5:41:cea6:69a9:ea1a:b363:19a5,240e:9a:24:36fc:9c1e:efe7:5962:96ff,2408:84ed:6:9008:5d3a:6276:17f8:c015,2408:84ed:20:8f:24a3:274b:3294:f5c6,2408:84ed:38:daba:f058:126d:b243:19cf,2408:84ed:78:bf94:84cb:b4a9:12e1:96c3,240e:470:341:7b92:c959:de5:53d:4269,2408:84ed:77:f711:b81b:c64b:b6ab:80b9,240e:9a:893:cd18:9c06:6565:1455:b49a,2408:8207:18a0:5480:1d8e:1823:9b2:1308,2408:84ed:16:2f24:48f0:d389:a63d:58e9,2408:84ed:3:5725:7d2e:9a47:d334:ad89,2408:84e1:105:3e40:6cc1:124d:f499:3183,2408:84e1:cc:862d:5ce6:4cba:a4e:1ad5,240e:ac:e811:7682:a81b:64df:ab3:60a,240e:ac:e035:b0ee:8945:c799:d946:4d0,2408:84f2:3c8:46c:926:e772:ed43:66e2,2408:84ed:39:5090:2139:ae3a:b00:4436,2408:805f:e321:3640:8d05:f702:4a42:e433,2001:e68:540e:1073:45ac:1bb:e1cd:1e5d";
//            String[] split = ip.split(",");
//
//            for(int i = 0; i < split.length; ++i) {
//            }
//        } catch (Exception var4) {
//            var4.printStackTrace();
//        }
//
//    }
//
//    static {
//        try {
//            init();
//        } catch (IOException var1) {
//            var1.printStackTrace();
//        }
//
//    }
//}
