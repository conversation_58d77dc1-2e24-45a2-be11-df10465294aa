//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package flink.util.ip;

import java.io.Serializable;

public class IpRelation implements Serializable {
    private String ipStart;
    private String ipEnd;
    private int ipCode;
    private String province;
    private String city;

    public IpRelation() {
    }

    public String getCity() {
        return this.city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public int getIpCode() {
        return this.ipCode;
    }

    public void setIpCode(int ipCode) {
        this.ipCode = ipCode;
    }

    public String getIpEnd() {
        return this.ipEnd;
    }

    public void setIpEnd(String ipEnd) {
        this.ipEnd = ipEnd;
    }

    public String getIpStart() {
        return this.ipStart;
    }

    public void setIpStart(String ipStart) {
        this.ipStart = ipStart;
    }

    public String getProvince() {
        return this.province;
    }

    public void setProvince(String province) {
        this.province = province;
    }
}
